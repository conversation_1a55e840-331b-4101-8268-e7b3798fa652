import re
import asyncio
import motor.motor_asyncio
from datetime import datetime
from typing import Optional, Dict, Any, List
import logging
import uuid

from db_utils import get_db_client
from tools.str_utils import remove_last_comma
from log_utils import debug
from collections import defaultdict

class SingleDocCacheManager:
    def __init__(self):
        self.client = get_db_client()
        self.cache_collection = self.client.reply_cache_collection
        self.str_cache_collection = self.client.reply_cache_str_collection

        self.doc_id = "dialogue_cache"
        self.doc_str_id = "dialogue_cache_str"
        self.cache_doc = None
        self.str_cache_doc = None
        self.question_templates = None
        self.str_question_templates = None
        self.str_qkv_map = defaultdict(dict) # {'q':{'k':'v'}}

        self.chat_tuple = [] #与大模型对话中累积的上一个问题，用户回复，下一个大模型回复的三元组，用于通话结束后加到缓存模板里

    async def add_chat_tuple(self, prev_q, user_reply, next_llm):
        self.chat_tuple.append((prev_q, user_reply, next_llm))

    async def flush_chat_tuple(self):
        for prev_q, user_reply, next_llm in self.chat_tuple:
            debug(f"-------------------写入缓存")
            debug(f"prev_q: {prev_q}")
            user_reply = await remove_last_comma(user_reply)
            debug(f"user_reply: {user_reply}")
            debug(f"next_llm: {next_llm}")
            await self.add_str_temp(prev_q, user_reply, next_llm)

    async def get_cache_doc(self):
        """获取缓存文档"""
        self.cache_doc = await self.cache_collection.find_one({"_id": self.doc_id})
        self.str_cache_doc = await self.str_cache_collection.find_one({"_id": self.doc_str_id})

        if not self.cache_doc:
            await self._init_cache_doc()
            self.cache_doc = await self.cache_collection.find_one({"_id": self.doc_id})

        if not self.str_cache_doc:
            await self._init_str_cache_doc()
            self.str_cache_doc = await self.str_cache_collection.find_one({"_id": self.doc_str_id})

        self.question_templates = self.cache_doc.get("question_templates", [])

        self.str_question_templates = self.str_cache_doc.get("question_templates", [])
        for q_template in self.str_question_templates:
            question_pattern = q_template.get("question_pattern", "")
            for reply_temp in q_template.get("reply_templates", []):
                user_reply_pattern = reply_temp.get('user_reply_pattern', '')
                next_question = reply_temp.get('next_question', '')
                kv_dic = self.str_qkv_map[question_pattern]
                if user_reply_pattern not in kv_dic:
                    kv_dic[user_reply_pattern] = {}
                kv_dic[user_reply_pattern] = next_question



    async def _init_cache_doc(self):
        """初始化缓存文档"""
        initial_doc = {
            "_id": self.doc_id,
            "question_templates": [],
            "stats": {
                "total_hits": 0,
                "last_updated": datetime.now()
            }
        }
        await self.cache_collection.insert_one(initial_doc)


    async def _init_str_cache_doc(self):
        """初始化缓存文档"""
        str_initial_doc = {
            "_id": self.doc_str_id,
            "question_templates": [],

        }
        await self.str_cache_collection.insert_one(str_initial_doc)

    async def _find_question_template(self, question_pattern: str):
        """查找已存在的问题模板"""
        for template in self.question_templates:
            if template.get("question_pattern") == question_pattern:
                return template
        return None

    async def _find_str_question_template(self, question_pattern: str):
        """查找已存在的问题模板"""
        for template in self.str_question_templates:
            if template.get("question_pattern") == question_pattern:
                return template
        return None

    async def add_str_temp(self,
                           question_pattern: str,
                           user_reply_pattern: str,
                           next_question: str):
        if self.cache_doc is None:
            await self.get_cache_doc()

        existing_q_template = await self._find_str_question_template(question_pattern)
        if existing_q_template:
            # 检查是否已有相同的回复模板
            for reply_template in existing_q_template.get("reply_templates", []):
                if reply_template.get("user_reply_pattern") == user_reply_pattern:
                    logging.info(f"模板已存在，跳过添加: {question_pattern} -> {user_reply_pattern}")
                    return reply_template["id"]

            # 添加新的回复模板到现有问题模板
            new_reply_template = {
                "id": str(uuid.uuid4())[:8],
                "user_reply_pattern": user_reply_pattern,
                "next_question": next_question,
                "hit_count": 0
            }

            await self.str_cache_collection.update_one(
                {
                    "_id": self.doc_str_id,
                    "question_templates.id": existing_q_template["id"]
                },
                {
                    "$push": {"question_templates.$.reply_templates": new_reply_template}
                }
            )

            logging.info(f"添加回复模板到现有问题: {new_reply_template['id']}")
            return new_reply_template["id"]

        else:
            # 创建新的问题模板
            new_q_template = {
                "id": str(uuid.uuid4())[:8],
                "question_pattern": question_pattern,
                "reply_templates": [
                    {
                        "id": str(uuid.uuid4())[:8],
                        "user_reply_pattern": user_reply_pattern,
                        "next_question": next_question,
                        "hit_count": 0
                    }
                ],
                "enabled": True,
                "created_at": datetime.now()
            }

            await self.str_cache_collection.update_one(
                {"_id": self.doc_str_id},
                {"$push": {"question_templates": new_q_template}}
            )

            logging.info(f"添加新问题模板: {new_q_template['id']}")
            return new_q_template["id"]



    async def add_template(self,
                           question_pattern: str,
                           user_reply_pattern: str,
                           next_question: str,
                           variables: Dict[str, str] = None,
                           tags: List[str] = None):
        """添加缓存模板（支持层级结构）"""
        if self.cache_doc is None:
            await self.get_cache_doc()

        # 查找是否已有相同的问题模板
        existing_q_template = await self._find_question_template(question_pattern)

        if existing_q_template:
            # 检查是否已有相同的回复模板
            for reply_template in existing_q_template.get("reply_templates", []):
                if reply_template.get("user_reply_pattern") == user_reply_pattern:
                    logging.info(f"模板已存在，跳过添加: {question_pattern} -> {user_reply_pattern}")
                    return reply_template["id"]

            # 添加新的回复模板到现有问题模板
            new_reply_template = {
                "id": str(uuid.uuid4())[:8],
                "user_reply_pattern": user_reply_pattern,
                "next_question": next_question,
                "variables": variables or {},
                "hit_count": 0
            }

            await self.cache_collection.update_one(
                {
                    "_id": self.doc_id,
                    "question_templates.id": existing_q_template["id"]
                },
                {
                    "$push": {"question_templates.$.reply_templates": new_reply_template}
                }
            )

            logging.info(f"添加回复模板到现有问题: {new_reply_template['id']}")
            return new_reply_template["id"]

        else:
            # 创建新的问题模板
            new_q_template = {
                "id": str(uuid.uuid4())[:8],
                "question_pattern": question_pattern,
                "reply_templates": [
                    {
                        "id": str(uuid.uuid4())[:8],
                        "user_reply_pattern": user_reply_pattern,
                        "next_question": next_question,
                        "variables": variables or {},
                        "hit_count": 0
                    }
                ],
                "tags": tags or [],
                "enabled": True,
                "created_at": datetime.now()
            }

            await self.cache_collection.update_one(
                {"_id": self.doc_id},
                {"$push": {"question_templates": new_q_template}}
            )

            logging.info(f"添加新问题模板: {new_q_template['id']}")
            return new_q_template["id"]

    async def find_cached_response(self,
                                   previous_question: str,
                                   user_reply: str) -> Optional[str]:
        """查找缓存的回复（层级匹配）"""

        if not self.question_templates:
            await self.get_cache_doc()

        for q_template in self.question_templates:
            if not q_template.get("enabled", True):
                continue

            try:
                # 匹配问题模式
                question_pattern = q_template.get("question_pattern", "")
                question_match = re.search(question_pattern, previous_question, re.IGNORECASE)
                if not question_match:
                    continue

                # 在该问题模板下匹配用户回复
                for reply_template in q_template.get("reply_templates", []):
                    user_pattern = reply_template.get("user_reply_pattern", "")
                    if not user_pattern:
                        continue

                    user_match = re.search(user_pattern, user_reply, re.IGNORECASE)
                    if not user_match:
                        continue

                    # 找到匹配，处理变量替换
                    next_question = reply_template["next_question"]
                    variables = reply_template.get("variables", {})

                    # 修正：统一从问题和回复中提取变量，按照顺序编号
                    if variables:
                        # 合并所有捕获组：问题的捕获组在前，用户回复的捕获组在后
                        all_groups = []

                        # 添加问题的捕获组
                        if question_match:
                            all_groups.extend(question_match.groups())

                        # 添加用户回复的捕获组
                        if user_match:
                            all_groups.extend(user_match.groups())

                        # 替换变量
                        for var_name, var_pattern in variables.items():
                            if var_pattern.startswith("$"):
                                try:
                                    group_num = int(var_pattern[1:])
                                    if group_num <= len(all_groups):
                                        var_value = all_groups[group_num - 1]  # 索引从0开始，但$1对应索引0

                                        # 关键修复：检查var_value是否为None
                                        if var_value is not None:
                                            next_question = next_question.replace(f"{{{var_name}}}", var_value)
                                        else:
                                            # 如果捕获组为None，使用空字符串或默认值
                                            logging.warning(f"捕获组 ${group_num} 为空，变量 {var_name} 使用空字符串")
                                            next_question = next_question.replace(f"{{{var_name}}}", "")
                                    else:
                                        logging.warning(
                                            f"捕获组索引 {group_num} 超出范围，总共 {len(all_groups)} 个捕获组")
                                except ValueError as ve:
                                    logging.error(f"变量模式解析错误: {var_pattern}, 错误: {ve}")
                                except Exception as ve:
                                    logging.error(f"变量替换错误: {var_name}={var_pattern}, 错误: {ve}")

                    # 更新命中次数
                    # await self.cache_collection.update_one(
                    #     {
                    #         "_id": self.doc_id,
                    #         "question_templates.id": q_template["id"],
                    #         "question_templates.reply_templates.id": reply_template["id"]
                    #     },
                    #     {
                    #         "$inc": {
                    #             "question_templates.$[qt].reply_templates.$[rt].hit_count": 1,
                    #             "stats.total_hits": 1
                    #         },
                    #         "$set": {
                    #             "stats.last_updated": datetime.now()
                    #         }
                    #     },
                    #     array_filters=[
                    #         {"qt.id": q_template["id"]},
                    #         {"rt.id": reply_template["id"]}
                    #     ]
                    # )
                    #
                    # logging.info(f"缓存命中: Q{q_template['id']} -> R{reply_template['id']}")
                    return next_question

            except Exception as e:
                logging.error(f"模板匹配错误: {e}")
                continue

        # logging.info("未找到匹配的缓存")
        if previous_question in self.str_qkv_map:
            user_reply_dic = self.str_qkv_map[previous_question]
            if user_reply in user_reply_dic:
                return user_reply_dic[user_reply]
        return None

    async def disable_question_template(self, question_id: str):
        """禁用问题模板"""
        await self.cache_collection.update_one(
            {
                "_id": self.doc_id,
                "question_templates.id": question_id
            },
            {"$set": {"question_templates.$.enabled": False}}
        )

    async def get_stats(self) -> Dict:
        """获取统计信息"""
        if not self.cache_doc:
            await self.get_cache_doc()

        if not self.cache_doc:
            return {"total_questions": 0, "total_replies": 0, "total_hits": 0}

        question_templates = self.cache_doc.get("question_templates", [])
        total_questions = len(question_templates)
        total_replies = sum(len(qt.get("reply_templates", [])) for qt in question_templates)
        enabled_questions = sum(1 for qt in question_templates if qt.get("enabled", True))

        return {
            "total_questions": total_questions,
            "enabled_questions": enabled_questions,
            "total_replies": total_replies,
            "total_hits": self.cache_doc.get("stats", {}).get("total_hits", 0),
            "last_updated": self.cache_doc.get("stats", {}).get("last_updated")
        }

    async def clear_all_templates(self):
        """清空所有模板"""
        await self.cache_collection.update_one(
            {"_id": self.doc_id},
            {
                "$set": {
                    "question_templates": [],
                    "stats": {
                        "total_hits": 0,
                        "last_updated": datetime.now()
                    }
                }
            }
        )


# 初始化示例模板
async def setup_initial_templates():
    cache_manager = SingleDocCacheManager()

    # 清空现有模板，重新加载
    # await cache_manager.clear_all_templates()
    await cache_manager.add_str_temp('请问您昨天进行锻炼了吗？','有锻炼','您昨天锻炼了多少下？')
    await cache_manager.add_str_temp('你在干什么', '没干什么', '那就快去玩游戏')

    # =================== 1. 身份确认模板 ===================

    # 1.1 身份确认 - 正确（修复：更宽松的匹配模式）
    await cache_manager.add_template(
        question_pattern=r"您好[，,]?我是武汉协和医院骨科的智能助理[，,]?请问是([\u4e00-\u9fa5]{2,6})吗[？?]?",
        user_reply_pattern=r"^(是+|对+|嗯+|恩+|没错|正确|好|行|可以|确认|确定|(是的)+|(对的)+|(是我)+|就是我|我就是|(我是)+|确实|没问题|好的|(对啊)+|(是啊)+|嗯啊|OK|ok|中|(对头)+|没毛病)$",
        next_question="{name}，请问您昨天进行锻炼了吗？",
        variables={"name": "$1"},  # $1 指向问题中的姓名捕获组
        tags=["identity_confirm", "positive"]
    )

    # 1.2 身份确认 - 不是本人
    await cache_manager.add_template(
        question_pattern=r"您好[，,]?我是武汉协和医院骨科的智能助理[，,]?请问是([\u4e00-\u9fa5]{2,6})吗[？?]?",
        user_reply_pattern=r".*(不是|不对|我不是|他不在|她不在|找错了|打错了|不是本人|不是我)",
        next_question="好的，请{name}本人接一下电话好吗？",
        variables={"name": "$1"},
        tags=["identity_confirm", "not_patient"]
    )

    # =================== 2. 锻炼状态确认模板 ===================

    # 2.1 锻炼确认 - 已锻炼
    await cache_manager.add_template(
        question_pattern=r"请问您昨天进行锻炼了吗[？?]?",
        user_reply_pattern=r"(是|对|嗯|已经|完成了|锻炼了|做了|进行了|练了|训练了)",
        next_question="您昨天锻炼了多少下？",
        tags=["exercise_status", "did_exercise"]
    )

    # 2.2 锻炼确认 - 未锻炼
    await cache_manager.add_template(
        question_pattern=r"请问您昨天进行锻炼了吗[？?]?",
        user_reply_pattern=r"(没有|没|不|未|忘了|没时间|没做|没进行|没练|没训练)",
        next_question="请问昨天没有锻炼是什么原因呢？",
        tags=["exercise_status", "no_exercise"]
    )

    # =================== 3. 锻炼次数相关模板（修复捕获组索引）===================

    # 3.1 锻炼次数统计 - 标准回复（修复变量索引）
    await cache_manager.add_template(
        question_pattern=r"您昨天锻炼了多少(?:下|次)[？?]?",
        user_reply_pattern=r".*?(?:我?昨天?锻炼了?|做了|完成了|练了)?(\d+)(?:下|次)",
        next_question="跟您确认下，您是昨天锻炼了{count}下，是吗？",
        variables={"count": "$1"},  # 修正：用户回复的第一个捕获组
        tags=["exercise_count"]
    )

    # 3.2 锻炼次数确认 - 确认正确
    await cache_manager.add_template(
        question_pattern=r"跟您确认下，您是昨天锻炼了(\d+)下，是吗[？?]?",
        user_reply_pattern=r"^(是|对|嗯|恩|没错|正确|确认|确定|是的|对的|没问题|对啊|是啊|好的)",
        next_question="很好，您昨天总共花了多长时间完成锻炼？",
        tags=["exercise_count_confirm", "correct"]
    )

    # =================== 4. 锻炼时长相关模板（支持中文数字）===================

    # 4.1 锻炼时长统计 - 数字时长（修复变量索引）
    await cache_manager.add_template(
        question_pattern=r"您昨天总共花了多长时间完成锻炼[？?]?",
        user_reply_pattern=r".*?(?:大约|差不多|约|大概|花了|用了|总共)?(\d+(?:\.\d+)?)\s*(分钟|小时|个小时)",
        next_question="训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
        variables={"time": "$1", "unit": "$2"},  # 修正索引
        tags=["exercise_duration"]
    )

    # 4.2 锻炼时长统计 - 中文数字时长
    await cache_manager.add_template(
        question_pattern=r"您昨天总共花了多长时间完成锻炼[？?]?",
        user_reply_pattern=r".*?(?:大约|差不多|约|大概|花了|用了|总共)?(一|二|三|四|五|六|七|八|九|十|十一|十二|十三|十四|十五|二十|三十|四十|五十|六十|七十|八十|九十|一百)(?:多)?分钟",
        next_question="训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
        tags=["exercise_duration", "chinese_numbers"]
    )

    # =================== 5. 锻炼感受相关模板 ===================

    # 5.1 锻炼感受 - 无不适
    await cache_manager.add_template(
        question_pattern=r"训练后感觉怎么样.*(?:有没有感到任何不适|感觉如何)[？?]?",
        user_reply_pattern=r"^(很好|挺好|还好|不错|感觉好|没有不适|没有|没|没什么不适|没问题|正常|舒服|挺舒服|感觉正常|没什么感觉)(?!.*(酸|痛|疼|不舒服|不适|难受|胀|累|疲劳))",
        next_question="很好，您使用的辅助仪器都还正常吧？",
        tags=["exercise_feeling", "no_discomfort"]
    )

    # 5.2 锻炼感受 - 有不适（腰部）
    await cache_manager.add_template(
        question_pattern=r"训练后感觉怎么样.*(?:有没有感到任何不适|感觉如何)[？?]?",
        user_reply_pattern=r".*(腰|腰部|后腰|腰背).*(酸|痛|疼|不舒服|不适|胀|累|疲劳|沉|重)",
        next_question="我了解您的情况了。这种腰部酸感在锻炼完休息后有没有缓解呢？",
        tags=["exercise_feeling", "waist_discomfort"]
    )

    # =================== 6. 不适感缓解情况模板 ===================

    # 6.1 不适感缓解 - 已缓解
    await cache_manager.add_template(
        question_pattern=r"这种.*(?:酸感|不适感|疼痛)在锻炼完休息后有没有缓解呢[？?]?",
        user_reply_pattern=r".*(缓解了|好了|没事了|恢复了|不酸了|不痛了|消失了|减轻了|好多了|舒服了|正常了)",
        next_question="很好，这是正常的锻炼反应，说明锻炼起到了效果。您使用的辅助仪器都还正常吧？",
        tags=["discomfort_relief", "relieved"]
    )

    # 6.2 不适感缓解 - 未缓解
    await cache_manager.add_template(
        question_pattern=r"这种.*(?:酸感|不适感|疼痛)在锻炼完休息后有没有缓解呢[？?]?",
        user_reply_pattern=r".*(没有缓解|一直|持续|还在|没好|还酸|还痛|还疼|还不舒服|到现在还).*(?:没缓解)?",
        next_question="这种不适感是今天才出现的，还是之前就有？",
        tags=["discomfort_relief", "not_relieved"]
    )

    # =================== 7. 不适感历史询问模板 ===================

    # 7.1 不适感历史 - 之前就有
    await cache_manager.add_template(
        question_pattern=r"这种.*(?:不适感|酸感|疼痛)是今天才出现的，还是之前就有[？?]?",
        user_reply_pattern=r".*(之前|以前|一直|本来|原来|早就|老早就).*就?有",
        next_question="好的，请问您是否向我们的医生反应过这个情况？",
        tags=["discomfort_history", "existing_symptom"]
    )

    # =================== 8. 医生反馈情况模板 ===================

    # 8.1 医生反馈 - 未反馈
    await cache_manager.add_template(
        question_pattern=r"请问您?是否向我们的医生反应过.*(?:这个情况|这种不适)[？?]?",
        user_reply_pattern=r"^(没有|没|不|未|还没有|忘了|没说|没告诉|没反应)",
        next_question="好的，我会把您的情况反馈给医生，他会在12小时内联络您。鉴于您本次训练结束后仍有不适感，建议您在医生给您下一步训练建议前，暂停训练。",
        tags=["doctor_feedback", "not_reported"]
    )

    # =================== 9. 暂停训练建议模板 ===================

    # 9.1 暂停训练建议 - 同意
    await cache_manager.add_template(
        question_pattern=r"建议您在医生给您下一步训练建议前，暂停训练。?",
        user_reply_pattern=r"^(好的|好|知道了|明白了|收到|了解|遵医嘱|听医生的|行|可以|没问题)",
        next_question="您使用的辅助仪器都还正常吧？",
        tags=["training_pause", "agreed"]
    )

    # =================== 10. 设备状态检查模板 ===================

    # 10.1 设备状态 - 正常
    await cache_manager.add_template(
        question_pattern=r"您使用的辅助仪器都还正常吧[？?]?",
        user_reply_pattern=r".*(正常|好的|挺好|还好|没问题|很稳|稳固|牢固|挺紧|很紧|绑得紧|固定得好|都挺好).*",
        next_question="您进行锻炼已经多久了？",
        tags=["equipment_status", "normal"]
    )

    # =================== 11. 锻炼持续时间模板（修复变量索引）===================

    # 11.1 锻炼持续时间 - 数字时长（修复变量索引）
    await cache_manager.add_template(
        question_pattern=r"您进行锻炼已经多久了[？?]?",
        user_reply_pattern=r".*?(?:锻炼|练|做|进行|开始|坚持|有|差不多|大约|约|快|将近|已经|差点|接近|快到|差不多到|从|自从)?.*?(\d+(?:\.\d+)?)\s*(个月|月份|月|周|星期|礼拜|天|日|年)",
        next_question="很好，{duration}{unit}的锻炼时间很不错，请您在身体没有不适感情况下坚持锻炼。",
        variables={"duration": "$1", "unit": "$2"},  # 修正索引
        tags=["total_exercise_duration"]
    )

    # 11.2 锻炼持续时间 - 中文数字表达（如"一个半月"）
    await cache_manager.add_template(
        question_pattern=r"您进行锻炼已经多久了[？?]?",
        user_reply_pattern=r".*(?:差不多|快到|将近|快|已经)?.*(一个半|两个半|三个半|半个)月",
        next_question="很好，您的锻炼时间很不错，请您在身体没有不适感情况下坚持锻炼。",
        tags=["total_exercise_duration", "chinese_expression"]
    )

    # =================== 12. 鼓励与建议模板 ===================

    # 12.1 锻炼坚持鼓励 - 积极回应
    await cache_manager.add_template(
        question_pattern=r".*请您在身体没有不适感情况下坚持锻炼。?",
        user_reply_pattern=r".*(好的|知道了|会的|坚持|继续|感谢|谢谢|一定会|会坚持|会继续).*",
        next_question="您做得很好。如果有任何不舒服，可以随时到医院来看，随时来找医生。",
        tags=["encouragement", "positive_response"]
    )

    # =================== 13. 医院就诊建议模板 ===================

    # 13.1 医院就诊建议 - 准备结束对话
    await cache_manager.add_template(
        question_pattern=r"如果有任何不舒服，可以随时到医院来看，随时来找医生。?",
        user_reply_pattern=r".*(好的|知道了|明白了|会的|谢谢|感谢|记住了|了解).*",
        next_question="还有其他问题需要我帮您解答的吗？",
        tags=["hospital_advice", "general_response"]
    )

    # =================== 14. 问题确认与结束模板 ===================

    # 14.1 最终问题确认 - 无其他问题
    await cache_manager.add_template(
        question_pattern=r"还有其他问题需要我帮您解答的吗[？?]?",
        user_reply_pattern=r"^(没有|没有了|没问题|没什么|不用了|就这样|可以了|谢谢|够了)",
        next_question="好的，今天就先这样，再见。",
        tags=["final_check", "no_more_questions"]
    )

    # =================== 15. 告别模板 ===================

    # 15.1 标准告别（修复：匹配任意告别回复）
    await cache_manager.add_template(
        question_pattern=r".*再见。?",
        user_reply_pattern=r".*(再见|谢谢|拜拜|bye|好的|麻烦了|辛苦了)",
        next_question="再见。",
        tags=["farewell", "standard"]
    )

    return cache_manager


async def main():
    cache_manager = await setup_initial_templates()

    # 测试缓存
    cached_reply = await cache_manager.find_cached_response(
        "您好，我是武汉协和医院骨科的智能助理，请问是王女士吗？",
        "我是"
    )
    print(f"缓存回复: {cached_reply}")

    # 测试另一种回复模式
    cached_reply2 = await cache_manager.find_cached_response(
        "您昨天锻炼了多少下？",
        "500下"
    )
    print(f"缓存回复2: {cached_reply2}")

    cached_reply3 = await cache_manager.find_cached_response(
        "您进行锻炼已经多久了？",
        "有3天了"
    )
    print(f"缓存回复3: {cached_reply3}")

    # 查看统计
    stats = await cache_manager.get_stats()
    print(f"统计信息: {stats}")

    test_dialogue = [
        ("您好，我是武汉协和医院骨科的智能助理，请问是张三吗？", "是的，我是。"),
        ("张三，请问您昨天进行锻炼了吗？", "是的，我昨天已经完成了锻炼。"),
        ("您昨天锻炼了多少下？", "我昨天锻炼了900下。"),
        ("跟您确认下，您是昨天锻炼了900下，是吗？", "是的。"),
        ("很好，您昨天总共花了多长时间完成锻炼？", "我昨天总共花了大约五十分钟。"),
        ("训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？", "锻炼过程中感觉还好，但甩久了之后会感到腰部有些酸。"),
        ("我了解您的情况了。这种腰部酸感在锻炼完休息后有没有缓解呢？",
         "锻炼完以后，酸胀感一直没有缓解，差不多过了2个小时还没缓解"),
        ("这种腰部酸感是今天才出现的，还是之前就有？", "其实之前就有一点点，但不是很严重。"),
        ("好的，请问您是否向我们的医生反应过这个情况？", "没有"),
        (
            "好的，我会把您的情况反馈给医生，他会在12小时内联络您。鉴于您本次训练结束后仍有不适感，建议您在医生给您下一步训练建议前，暂停训练。",
            "好的"),
        ("您使用的辅助仪器都还正常吧？", "是的，都挺好的。绑在腿上的机器绑得挺紧，甩的时候也很稳固。"),
        ("您进行锻炼已经多久了？", "我这次差不多快到一个半月了。"),
        ("很好，一个半月的锻炼时间很不错，请您在身体没有不适感情况下坚持锻炼。", "好的，谢谢您们的关心。"),
        ("您做得很好。如果有任何不舒服，可以随时到医院来看，随时来找医生。", "好的，谢谢您。"),
        ("还有其他问题需要我帮您解答的吗？", "没有了"),
        ("好的，今天就先这样，再见。", "再见。"),
        ("你在干什么", "没干什么")
    ]

    print("=== 测试完整对话流程 ===")
    for i, (question, reply) in enumerate(test_dialogue, 1):
        cached_response = await cache_manager.find_cached_response(question, reply)
        print(f"{i}. 问题: {question}")
        print(f"   回复: {reply}")
        print(f"   缓存: {cached_response}")
        print("-" * 80)


if __name__ == '__main__':
    asyncio.run(main())