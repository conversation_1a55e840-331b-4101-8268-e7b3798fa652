#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试邮件内容中的患者编号
"""

import asyncio
from datetime import datetime
from tools.patient_email_sender import PatientEmailSender
from log_utils import info


async def test_email_content():
    """测试邮件内容生成"""
    info("=== 测试邮件内容中的患者编号 ===")
    
    # 模拟通话记录数据（没有患者编号字段）
    patient_data_without_id = {
        "患者名字": "张三",
        # 注意：这里故意不包含"患者编号"字段
        "手机号": "18971492577",
        "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "记录日期": datetime.now().strftime("%Y-%m-%d"),
        "拨号状态": "通话结束",
        "训练完成情况": "未完成",
        "训练次数": "50次",
        "训练时长": "8分钟",
        "是否有不适感": "是",
        "不适感内容": "膝盖疼痛严重",
        "不适感锻炼结束后是否恢复": "否",
        "是否需要医生人工和患者联系": "是",
        "锻炼辅助仪器是否有问题": "否",
        "锻炼辅助仪器问题内容": "正常"
    }
    
    try:
        sender = PatientEmailSender()
        
        # 获取患者编号
        patient_id = patient_data_without_id.get('患者编号')
        if not patient_id:
            patient_info = await sender.doctor_manager.get_patient_info_from_call_record(patient_data_without_id)
            if patient_info:
                patient_id = patient_info.get('patient_id', 'N/A')
            else:
                patient_id = 'N/A'
        
        info(f"查询到的患者编号: {patient_id}")
        
        # 创建增强的患者数据
        enhanced_patient_data = patient_data_without_id.copy()
        enhanced_patient_data['患者编号'] = patient_id
        
        # 生成邮件内容
        email_content = sender._build_email_content(enhanced_patient_data)
        
        info("生成的邮件内容:")
        print("\n" + "="*60)
        print(email_content)
        print("="*60 + "\n")
        
        # 检查邮件内容中的患者编号
        if f"患者编号：{patient_id}" in email_content and patient_id != 'N/A':
            info("✅ 邮件内容中包含正确的患者编号")
        else:
            info("❌ 邮件内容中患者编号不正确")
            
        # 检查是否还有N/A
        if "患者编号：N/A" in email_content:
            info("❌ 邮件内容中仍然显示N/A")
        else:
            info("✅ 邮件内容中没有N/A")
            
    except Exception as e:
        info(f"测试过程中出错: {e}")


async def main():
    """主测试函数"""
    try:
        await test_email_content()
    except Exception as e:
        info(f"测试过程中出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
