"""
语音处理器：负责处理连续语音识别事件和TTS回复
"""

import json
import asyncio

from tools.str_utils import choice_interval_speak
from log_utils import debug, info, warning, error
from AI_response import AIResponse

class SpeechHandler:
    def __init__(self, rpc_utils):
        self.rpc_utils = rpc_utils
        self.channel_uuid = None
        self.is_listening = False
        self.ai_response = None
        self.call_manager = None
        self.doc_cache = None

        self.tts_play_finished = True
        self.accumulated_user_texts = []

        self.detect_Partial_speech = False
        self.pre_trans_word = False # 是否播放了过渡语，避免连续两次播放过渡语

    def set_call_manager(self, call_manager):
        self.call_manager = call_manager

    def set_channel_uuid(self, channel_uuid):
        """设置当前通话的Channel UUID"""
        self.channel_uuid = channel_uuid

    def set_ai_response(self, Ai_response: AIResponse):
        self.ai_response = Ai_response
        self.doc_cache = self.ai_response.doc_cache
    
    async def start_listening(self):
        """开始连续语音识别"""
        if not self.channel_uuid:
            error("错误：未设置Channel UUID")
            return False
            
        info("启动连续语音识别...")
        response = await self.rpc_utils.start_continuous_asr(self.channel_uuid)

        if response.get("result", {}).get("code") == 202:
            self.is_listening = True
            info("连续语音识别已成功启动")
            return True
        else:
            warning("启动连续语音识别失败: %s", response)
            return False
    
    async def stop_listening(self):
        """停止连续语音识别"""
        if self.is_listening and self.channel_uuid:
            info("停止连续语音识别...")
            await self.rpc_utils.stop_detect_speech(self.channel_uuid)
            self.is_listening = False

    async def handle_tts_finish_event(self, message):
        """处理tts播放完毕事件"""
        try:

            debug("🎤 收到TTS播放完毕事件:")
            debug("事件内容: %s", message)
            params = message["params"]
            # 标记TTS播放完成
            if self.ai_response:
                self.ai_response.tts_play_finished = True
            # await self._process_speech_result(params)

        except Exception as e:
            error("处理TTS播放完毕事件出错: %s", e)
            import traceback
            traceback.print_exc()
    
    async def handle_speech_event(self, message):
        """处理语音识别事件"""
        try:

            # debug("🎤 收到语音识别事件:")
            # debug("事件内容: %s", message)
            params = message["params"]
            await self._process_speech_result(params)
                    
        except Exception as e:
            error("处理语音识别事件出错: %s", e)
            import traceback
            traceback.print_exc()
    
    async def _process_speech_result(self, result_data):
        """处理语音识别结果"""
        if "type" in result_data:
            result_type = result_data["type"]
            
            if result_type == "Speech.Begin":
                debug("开始检测到语音")
                
            elif result_type == "Speech.Partial":
                # 中间识别结果
                if "text" in result_data:
                    debug("中间识别结果: %s", result_data['text'])
                    self.detect_Partial_speech = True
                    pass
                    
            elif result_type == "Speech.End":
                # 最终识别结果
                if "text" in result_data:
                    final_text = result_data["text"]
                    confidence = result_data.get("confidence", 0.0)
                    info("最终识别结果: %s (置信度: %s)", final_text, confidence)
                    
                    # 处理识别到的文本
                    await self._handle_recognized_text(final_text)
                    self.detect_Partial_speech = False
                    
            elif result_type == "Speech.Merged":
                # 合并的识别结果
                if "text" in result_data:
                    info("合并识别结果: %s", result_data['text'])
                    # await self._handle_recognized_text(result_data['text'])
                    
            elif result_type == "ERROR":
                speech_error = result_data.get("error", "未知错误")
                warning("识别错误: %s", speech_error)
                
                if speech_error in ["no_input", "speech_timeout"]:
                    info("检测到超时，等待用户继续说话...")
    
    async def _handle_recognized_text(self, text):
        """处理最终识别到的文本"""
        if not text or text.strip() == "":
            debug("识别到空文本，忽略")
            return
            
        text = text.strip()
        if str(text).startswith('。'):
            text = text[1:]
        info("处理用户输入: %s", text)
        
        # 特殊指令处理
        if text in ["再见"]:
            info("收到结束指令")
            await self._say_goodbye()
            return
            
        # AI处理
        await self.add_acc_user_text(text)

        # response_text, code =
        # if code < 0:
        #     await self.close_call()
        #     return


    async def talk_loop(self):
        # 启动连续语音识别
        success = await self.start_listening()
        if success:
            info("连续语音识别已启动，开始对话...")
        else:
            warning("启动语音识别失败，挂断电话")
            await self.rpc_utils.hangup(self.channel_uuid)

        # 播放欢迎语
        welcome_text = self.ai_response.init_response
        info(f"播放TTS: {welcome_text}")
        await self._speak_async(welcome_text)
        await self.clean_acc_user_texts()

        while self.call_manager.running:
            if self.tts_play_finished: # tts 播放完毕，读取累积的用户输入文字，得到回复，播放tts
                if len(self.accumulated_user_texts) > 0 :
                    all_input = ''.join(self.accumulated_user_texts)

                    # ---------------- start ------------------------
                    # response_task = asyncio.create_task(self.ai_response.get_response(all_input))
                    # done, pending = await asyncio.wait([response_task], timeout=1.4)
                    #
                    # if not done:  # 超时了
                    #     await self._speak_async('请稍等')
                    #     reply, code = await response_task
                    # else:
                    #     reply, code = response_task.result()
                    #     warning(f"reply: {reply}")
                    #
                    # # reply, code = '我是武汉协和医院骨科的智能助理，刚刚跟您确认了，您昨天锻炼不到500下，对这个数量确认没问题吧', 0
                    # if code < 0:
                    #     await self.close_call()
                    #     self.call_manager.running = False
                    #     return
                    # info(f"用户说: {all_input}")
                    # info(f"AI回复: {reply}")
                    # await self._speak_async(reply)
                    # ---------------- end ------------------------

                    # ---------------- start ------------------------
                    # reply = ''
                    # async for chunk, code in self.ai_response.get_response_stream(all_input):
                    #     if code < 0:
                    #         await self.close_call()
                    #         self.call_manager.running = False
                    #         return
                    #     await self._speak_async(chunk)
                    #     reply += chunk
                    # info(f"用户说: {all_input}")
                    # info(f"AI回复: {reply}")
                    # ---------------- end ------------------------

                    await self.clean_acc_user_texts()
                    await self._speak_loop(all_input)


            await asyncio.sleep(0.01)

    async def _user_in_speech(self, all_input):
        while self.detect_Partial_speech and len(self.accumulated_user_texts) == 0:
            await asyncio.sleep(0.01)

        if len(self.accumulated_user_texts) > 0:
            combine_input = ''.join([all_input, *self.accumulated_user_texts])
            await self.add_acc_user_text(combine_input)
            return True

        return False

    async def _speak_loop(self, all_input):
        # ---------------- start ------------------------
        reply = ''
        # 创建流式响应的异步迭代器
        stream_iterator = self.ai_response.get_response_stream(all_input).__aiter__()

        # 创建获取第一个chunk的任务
        first_chunk_task = asyncio.create_task(stream_iterator.__anext__())
        done, pending = await asyncio.wait([first_chunk_task], timeout=0.5)

        if not done:  # 超时了
            in_speech = await self._user_in_speech(all_input)
            if in_speech: return

            if self.pre_trans_word == False:
                s = await choice_interval_speak()
                await self._speak_async(s) # 播放过渡音
                self.pre_trans_word = True
            chunk, code = await first_chunk_task  # 继续等待第一个chunk
        else:
            chunk, code = first_chunk_task.result()
            if code == 1: #缓存回复，此时不要立即播放，给予一定间隔，对方可能接着在说话
                await asyncio.sleep(0.3)
                in_speech = await self._user_in_speech(all_input)
                if in_speech: return

        # 处理第一个chunk
        if code < 0:
            await self.close_call()
            self.call_manager.running = False
            return
        reply += chunk
        # 正要播放第一个TTS时，检测到用户在说话，停止播放，等用户说完，把前后内容结合再通过大模型处理
        in_speech = await self._user_in_speech(all_input)
        if in_speech: return
        # -------------------end -----------------------
        last_llm_content = await self.ai_response.llm_client.get_last_chat(content_only=True)

        await self.ai_response.llm_client.add_user_history(all_input)
        await self._speak_async(chunk)


        # 继续处理剩余的chunks
        try:
            async for chunk, code in stream_iterator:
                if len(self.accumulated_user_texts) > 0: # 用户说话打断
                    break
                else:
                    await self._speak_async(chunk)
                    reply += chunk
        except StopAsyncIteration:
            pass  # 流结束

        info(f"用户说: {all_input}")
        info(f"AI回复: {reply}")
        self.pre_trans_word = False
        
        await self.ai_response.llm_client.add_ai_history(reply)
        # 1表示缓存返回, 只记录大模型返回的结果到模板中：
        if code == 0 :
            await self.doc_cache.add_chat_tuple(last_llm_content, all_input, reply)
        # ---------------- end ------------------------

    async def _speak_async(self, text):
        """异步播放TTS（不阻塞主流程）"""
        try:
            self.tts_play_finished = False
            tts_res = await self._speak(text)
            self.tts_play_finished = True
            
            # 检查是否需要结束通话
            if '再见' in text:
                await asyncio.sleep(1)
                await self.close_call()
                
            return tts_res
        except asyncio.CancelledError:
            info("TTS播放被取消")
            raise
        except Exception as e:
            error("TTS播放异常: %s", e)
            self.tts_play_finished = True
    
    async def _speak(self, text):
        """播放TTS"""
        if not self.channel_uuid:
            error("_speak tts 错误：未设置Channel UUID")
            return
            
        info("TTS播放: %s", text)
        tts_res = await self.rpc_utils.play_tts(self.channel_uuid, text)
        '''
        tts_res: {'jsonrpc': '2.0', 'id': 'tts-ea3a3cd2-23ae-4132-926e-293310661c57', 'result': {'node_uuid': '0d5c9477-db5d-47ef-8924-eb4b0d1fa328', 'uuid': '895aed86-4cf9-402f-9d31-17af4b0778d6', 'code': 200, 'message': 'OK'}}
        '''
        # info(tts_res)
        return tts_res
    
    async def _say_goodbye(self):
        """说再见并结束通话"""
        # goodbye_text = "好的，感谢您的通话，再见！"
        # await self._speak(goodbye_text)
        
        # 等待一秒后挂断
        await asyncio.sleep(1)
        await self.stop_listening()
        await self.rpc_utils.hangup(self.channel_uuid)

    async def close_call(self):
        # 取消正在进行的TTS任务
        await self.stop_listening()
        await self.rpc_utils.hangup(self.channel_uuid)


    async def add_acc_user_text(self, text):
        self.accumulated_user_texts.append(text)


    async def clean_acc_user_texts(self):
        self.accumulated_user_texts = []