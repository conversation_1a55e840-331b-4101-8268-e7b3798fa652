#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试患者邮件发送功能
"""

import asyncio
import sys
from datetime import datetime

from tools.patient_email_sender import send_patient_report_email_async
from db_utils import doctor_patient_manager, db_client
from log_utils import info, warning, error


async def test_doctor_patient_relationship():
    """测试医生-患者关联查询功能"""
    info("=== 测试医生-患者关联查询功能 ===")
    
    try:
        # 测试根据患者编号查找医生
        info("1. 测试根据患者编号查找医生...")
        doctor = await doctor_patient_manager.get_doctor_by_patient_id("P001")
        if doctor:
            info(f"找到医生: {doctor.get('name')} ({doctor.get('email')})")
        else:
            warning("未找到对应的医生")
        
        # 测试根据患者手机号查找医生
        info("2. 测试根据患者手机号查找医生...")
        doctor_email = await doctor_patient_manager.get_doctor_email_by_patient_phone("18912345678")
        if doctor_email:
            info(f"找到医生邮箱: {doctor_email}")
        else:
            warning("未找到对应的医生邮箱")
        
        # 查看数据库中的实际数据
        info("3. 查看数据库中的人员数据...")
        personnel = await db_client.get_all_personnel()
        info(f"数据库中共有 {len(personnel)} 个人员记录:")
        for person in personnel[:5]:  # 只显示前5个
            role = person.get('role', 'N/A')
            name = person.get('name', 'N/A')
            email = person.get('email', 'N/A')
            patient_id = person.get('patient_id', 'N/A')
            assigned_doctor = person.get('assigned_doctor', 'N/A')
            info(f"  - {name} ({role}): email={email}, patient_id={patient_id}, assigned_doctor={assigned_doctor}")
            
    except Exception as e:
        error(f"测试医生-患者关联查询时出错: {e}")


async def test_email_sending():
    """测试邮件发送功能"""
    info("=== 测试邮件发送功能 ===")
    
    # 构造测试数据
    test_patient_data = {
        "患者名字": "测试患者",
        "患者编号": "TEST001",
        "手机号": "13800138000",
        "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "记录日期": datetime.now().strftime("%Y-%m-%d"),
        "拨号状态": "通话结束",
        "训练完成情况": "未完成",
        "训练次数": "50次",
        "训练时长": "8分钟",
        "是否有不适感": "是",
        "不适感内容": "膝盖疼痛严重",
        "不适感锻炼结束后是否恢复": "否",
        "是否需要医生人工和患者联系": "是",
        "锻炼辅助仪器是否有问题": "否",
        "锻炼辅助仪器问题内容": "正常"
    }
    
    try:
        info("正在发送测试邮件...")
        result = await send_patient_report_email_async(test_patient_data)
        
        info(f"邮件发送结果: {result}")
        
        if result["success"]:
            info("✅ 测试邮件发送成功!")
            details = result.get("details", {})
            info(f"  患者: {details.get('patient_name')}")
            info(f"  医生邮箱: {details.get('doctor_email')}")
            info(f"  邮件主题: {details.get('subject')}")
        else:
            warning(f"❌ 测试邮件发送失败: {result['error']}")
            if result.get("details"):
                warning(f"详细信息: {result['details']}")
                
    except Exception as e:
        error(f"测试邮件发送时出错: {e}")


async def test_with_real_data():
    """使用真实数据测试"""
    info("=== 使用真实数据测试 ===")
    
    try:
        # 获取最近的通话记录
        info("获取最近的通话记录...")
        
        # 这里我们需要查询实际的通话记录
        # 由于我们不知道具体的数据，我们先查看数据库中的记录
        
        # 获取需要医生联系的患者记录
        need_contact_records = await db_client.get_patients_needing_doctor_contact()
        
        if need_contact_records:
            info(f"找到 {len(need_contact_records)} 条需要医生联系的记录")
            
            # 取第一条记录进行测试
            test_record = need_contact_records[0]
            info(f"使用记录: {test_record.get('患者名字')} ({test_record.get('手机号')})")
            
            # 发送邮件
            result = await send_patient_report_email_async(test_record)
            
            if result["success"]:
                info("✅ 真实数据邮件发送成功!")
            else:
                warning(f"❌ 真实数据邮件发送失败: {result['error']}")
        else:
            info("数据库中没有需要医生联系的记录")
            
    except Exception as e:
        error(f"使用真实数据测试时出错: {e}")


async def main():
    """主测试函数"""
    info("开始测试患者邮件发送功能...")
    
    try:
        # 测试1: 医生-患者关联查询
        await test_doctor_patient_relationship()

        print("\n" + "="*60 + "\n")

        # 测试2: 使用真实患者编号测试
        info("=== 使用真实患者编号测试 ===")
        real_patient_data = {
            "患者名字": "张三",
            "患者编号": "P0001",  # 使用真实的患者编号
            "手机号": "18971492577",
            "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
            "记录日期": datetime.now().strftime("%Y-%m-%d"),
            "拨号状态": "通话结束",
            "训练完成情况": "未完成",
            "训练次数": "50次",
            "训练时长": "8分钟",
            "是否有不适感": "是",
            "不适感内容": "膝盖疼痛严重",
            "不适感锻炼结束后是否恢复": "否",
            "是否需要医生人工和患者联系": "是",
            "锻炼辅助仪器是否有问题": "否",
            "锻炼辅助仪器问题内容": "正常"
        }

        result = await send_patient_report_email_async(real_patient_data)
        if result["success"]:
            info("✅ 真实患者编号测试成功!")
        else:
            warning(f"❌ 真实患者编号测试失败: {result['error']}")

        print("\n" + "="*60 + "\n")

        # 测试3: 邮件发送功能
        await test_email_sending()

        print("\n" + "="*60 + "\n")

        # 测试4: 使用真实数据
        await test_with_real_data()
        
    except Exception as e:
        error(f"测试过程中出错: {e}")
    finally:
        # 关闭数据库连接
        await db_client.close()
        info("测试完成，数据库连接已关闭")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        warning("测试被用户中断")
    except Exception as e:
        error(f"测试启动失败: {e}")
        sys.exit(1)
