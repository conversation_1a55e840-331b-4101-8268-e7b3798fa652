# -*- coding: utf-8 -*-#

# 使用 smtplib 模块发送纯文本邮件
import smtplib
import ssl
import asyncio
from email.message import EmailMessage
from typing import Dict, Any
from datetime import datetime


async def send_email_async(subject: str, body: str, to_email: str = '<EMAIL>') -> Dict[str, Any]:
    """
    异步发送邮件

    Args:
        subject: 邮件主题
        body: 邮件内容
        to_email: 收件人邮箱

    Returns:
        发送结果字典，包含success, error, details等字段
    """
    try:
        return await asyncio.get_event_loop().run_in_executor(
            None, _send_email_sync, subject, body, to_email
        )
    except Exception as e:
        return {
            "success": False,
            "error": f"邮件发送异常: {str(e)}",
            "details": None,
            "timestamp": datetime.now().isoformat()
        }


def _send_email_sync(subject: str, body: str, to_email: str) -> Dict[str, Any]:
    """
    同步发送邮件的内部实现

    Args:
        subject: 邮件主题
        body: 邮件内容
        to_email: 收件人邮箱

    Returns:
        发送结果字典
    """
    try:
        auth_code = 'TGHOGOETPSCETEOU'
        EMAIL_ADDRESS = "<EMAIL>"  # 邮箱的地址
        EMAIL_PASSWORD = auth_code  # 授权码

        # 创建SSL上下文
        context = ssl.create_default_context()

        # 创建邮件消息
        msg = EmailMessage()
        msg['subject'] = subject  # 邮件标题
        msg['From'] = EMAIL_ADDRESS  # 邮件发件人
        msg['To'] = to_email  # 邮件的收件人
        msg.set_content(body)  # 使用set_content()方法设置邮件的主体内容

        # 发送邮件
        with smtplib.SMTP_SSL("smtp.163.com", 465, context=context) as smtp:
            # 连接成功后使用login方法登录自己的邮箱
            smtp.login(EMAIL_ADDRESS, EMAIL_PASSWORD)
            # 发送邮件
            smtp.send_message(msg)

        return {
            "success": True,
            "error": None,
            "details": {
                "from": EMAIL_ADDRESS,
                "to": to_email,
                "subject": subject
            },
            "timestamp": datetime.now().isoformat()
        }

    except smtplib.SMTPAuthenticationError as e:
        return {
            "success": False,
            "error": f"邮箱认证失败: {str(e)}",
            "details": {"error_type": "authentication"},
            "timestamp": datetime.now().isoformat()
        }
    except smtplib.SMTPRecipientsRefused as e:
        return {
            "success": False,
            "error": f"收件人邮箱被拒绝: {str(e)}",
            "details": {"error_type": "recipient_refused"},
            "timestamp": datetime.now().isoformat()
        }
    except smtplib.SMTPException as e:
        return {
            "success": False,
            "error": f"SMTP错误: {str(e)}",
            "details": {"error_type": "smtp_error"},
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"邮件发送失败: {str(e)}",
            "details": {"error_type": "general_error"},
            "timestamp": datetime.now().isoformat()
        }


def send_email(subject, body, to_email='<EMAIL>'):
    """
    同步版本的邮件发送函数（保持向后兼容）
    """
    result = _send_email_sync(subject, body, to_email)
    if result["success"]:
        print("邮件发送成功")
    else:
        print(f"邮件发送失败: {result['error']}")
    return result


async def test_async_email():
    """测试异步邮件发送"""
    result = await send_email_async(
        subject="测试邮件",
        body="这是一封测试邮件",
        to_email="<EMAIL>"
    )
    print(f"发送结果: {result}")


if __name__ == '__main__':
    # 测试同步版本
    send_email('测试邮件', '这是一封测试邮件')

    # 测试异步版本
    # asyncio.run(test_async_email())