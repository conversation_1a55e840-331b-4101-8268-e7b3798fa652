# -*- coding: utf-8 -*-

"""
患者报告邮件发送模块
参照send_patient_report_async函数，使用邮件发送患者不适感报告
"""

import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

from .send_email import send_email_async
from ..db_utils import doctor_patient_manager
from ..log_utils import info, warning, error


class PatientEmailSender:
    """患者报告邮件发送器"""
    
    def __init__(self):
        """初始化邮件发送器"""
        self.doctor_manager = doctor_patient_manager
    
    async def send_patient_report_email(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送患者训练情况报告邮件
        
        Args:
            patient_data: 患者数据字典，包含以下字段：
                - 患者名字: 患者姓名
                - 患者编号: 患者编号（可选，如果没有则通过手机号查找）
                - 手机号: 患者手机号
                - 不适感内容: 不适感描述
                - 其他通话记录字段...
        
        Returns:
            发送结果字典，包含success, error, details等字段
        """
        try:
            # 提取基本信息
            patient_name = patient_data.get('患者名字', 'N/A')
            patient_phone = patient_data.get('手机号', 'N/A')
            discomfort_content = patient_data.get('不适感内容', 'N/A')
            
            # 获取患者编号
            patient_id = patient_data.get('患者编号')
            if not patient_id:
                # 如果没有患者编号，尝试从数据库查找
                patient_info = await self.doctor_manager.get_patient_info_from_call_record(patient_data)
                if patient_info:
                    patient_id = patient_info.get('patient_id', 'N/A')
                else:
                    patient_id = 'N/A'
            
            # 查找对应的医生邮箱
            doctor_email = None
            if patient_id != 'N/A':
                doctor_email = await self.doctor_manager.get_doctor_email_by_patient_id(patient_id)
            
            if not doctor_email:
                # 如果通过患者编号找不到，尝试通过手机号查找
                doctor_email = await self.doctor_manager.get_doctor_email_by_patient_phone(patient_phone)
            
            if not doctor_email:
                return {
                    "success": False,
                    "error": f"未找到患者 {patient_name}({patient_phone}) 对应的医生邮箱",
                    "details": {
                        "patient_name": patient_name,
                        "patient_phone": patient_phone,
                        "patient_id": patient_id
                    },
                    "timestamp": datetime.now().isoformat()
                }
            
            # 构建邮件主题
            subject = f"{patient_name}-{patient_id}-{patient_phone}-{discomfort_content}"
            
            # 构建邮件内容
            email_body = self._build_email_content(patient_data)
            
            # 发送邮件
            info(f"正在向医生邮箱 {doctor_email} 发送患者 {patient_name} 的报告邮件")
            
            email_result = await send_email_async(
                subject=subject,
                body=email_body,
                to_email=doctor_email
            )
            
            if email_result["success"]:
                info(f"✅ 患者报告邮件发送成功: {patient_name} -> {doctor_email}")
                return {
                    "success": True,
                    "error": None,
                    "details": {
                        "patient_name": patient_name,
                        "patient_phone": patient_phone,
                        "patient_id": patient_id,
                        "doctor_email": doctor_email,
                        "subject": subject,
                        "email_result": email_result
                    },
                    "timestamp": datetime.now().isoformat()
                }
            else:
                error(f"❌ 患者报告邮件发送失败: {email_result['error']}")
                return {
                    "success": False,
                    "error": f"邮件发送失败: {email_result['error']}",
                    "details": {
                        "patient_name": patient_name,
                        "patient_phone": patient_phone,
                        "patient_id": patient_id,
                        "doctor_email": doctor_email,
                        "email_result": email_result
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            error(f"发送患者报告邮件时出错: {str(e)}")
            return {
                "success": False,
                "error": f"发送患者报告邮件时出错: {str(e)}",
                "details": None,
                "timestamp": datetime.now().isoformat()
            }
    
    def _build_email_content(self, patient_data: Dict[str, Any]) -> str:
        """
        构建邮件内容
        
        Args:
            patient_data: 患者数据字典
            
        Returns:
            格式化的邮件内容字符串
        """
        # 获取训练完成状态
        training_status = patient_data.get('训练完成情况', 'N/A')
        training_emoji = "✅" if training_status == "完成" else "⚠️"
        
        # 获取不适感状态
        discomfort_status = patient_data.get('是否有不适感', 'N/A')
        discomfort_emoji = "✅" if discomfort_status == "否" else "🚨"
        
        # 获取设备状态
        device_status = patient_data.get('锻炼辅助仪器是否有问题', 'N/A')
        device_emoji = "✅" if device_status == "否" else "⚠️"
        
        # 获取是否需要医生联系
        doctor_contact = patient_data.get('是否需要医生人工和患者联系', 'N/A')
        doctor_emoji = "✅" if doctor_contact == "否" else "🚨"
        
        email_content = f"""患者训练情况报告
{'='*50}

👤 患者信息
患者姓名：{patient_data.get('患者名字', 'N/A')}
患者编号：{patient_data.get('患者编号', 'N/A')}
联系电话：{patient_data.get('手机号', 'N/A')}
通话时间：{patient_data.get('通话时间', 'N/A')}
拨号状态：{patient_data.get('拨号状态', 'N/A')}

🏃 训练情况
训练完成情况：{training_status} {training_emoji}
训练次数：{patient_data.get('训练次数', 'N/A')}
训练时长：{patient_data.get('训练时长', 'N/A')}

🏥 健康状况
是否有不适感：{discomfort_status} {discomfort_emoji}
不适感内容：{patient_data.get('不适感内容', 'N/A')}
锻炼结束后不适感是否恢复：{patient_data.get('不适感锻炼结束后是否恢复', 'N/A')}

🔧 设备状况
锻炼辅助仪器是否有问题：{device_status} {device_emoji}
设备问题详情：{patient_data.get('锻炼辅助仪器问题内容', 'N/A')}

👨‍⚕️ 医生介入
是否需要医生人工联系：{doctor_contact} {doctor_emoji}

{'='*50}
报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

此邮件由AI电话随访系统自动生成。
如有疑问，请联系系统管理员。
"""
        
        return email_content


# 便捷函数
async def send_patient_report_email_async(patient_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷函数：发送患者报告邮件
    
    Args:
        patient_data: 患者数据字典
        
    Returns:
        发送结果字典
    """
    sender = PatientEmailSender()
    return await sender.send_patient_report_email(patient_data)


# 使用示例
async def main():
    """使用示例"""
    
    # 示例患者数据
    patient_data = {
        "患者名字": "张三",
        "患者编号": "P001",
        "手机号": "18912345678",
        "通话时间": "2025-06-21 14:30",
        "拨号状态": "通话结束",
        "训练完成情况": "未完成",
        "训练次数": "50次",
        "训练时长": "8分钟",
        "是否有不适感": "是",
        "不适感内容": "膝盖疼痛",
        "不适感锻炼结束后是否恢复": "否",
        "是否需要医生人工和患者联系": "是",
        "锻炼辅助仪器是否有问题": "否",
        "锻炼辅助仪器问题内容": "正常"
    }
    
    # 发送邮件
    result = await send_patient_report_email_async(patient_data)
    
    print(f"发送结果: {result}")
    
    if result["success"]:
        print("✅ 邮件发送成功")
    else:
        print(f"❌ 邮件发送失败: {result['error']}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
