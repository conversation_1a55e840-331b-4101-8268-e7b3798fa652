# 医疗随访系统数据库模型总结

> 基于实际数据库内容分析生成
> 数据库名称: med_call_records

## 核心数据模型

### 1. 通话记录 (call_records) - 核心业务数据
```
主键: _id (ObjectId)
患者信息:
  - 患者名字 (string)
  - 患者编号 (string) 
  - 手机号 (string)

通话信息:
  - 通话时间 (string)
  - 记录日期 (string)
  - 拨号状态 (string): "拨号中" | "无法接通" | "通话结束" | "通话结束-信息提取"
  - 通话时长 (int): 通话时长（秒），从接通到挂断的精确时长
  - 对话历史记录 (array): [{role, content}]

锻炼跟踪:
  - 训练完成情况 (string): "完成" | "未完成"
  - 训练次数 (string)
  - 训练时长 (string)
  - 是否有不适感 (string): "是" | "否"
  - 不适感内容 (string)
  - 锻炼辅助仪器是否有问题 (string): "是" | "否"
  - 锻炼辅助仪器问题内容 (string)

医疗跟进:
  - 是否需要医生人工和患者联系 (string): "是" | "否"

系统字段:
  - 创建时间 (datetime)
  - updated_at (datetime)
```

### 2. 人员管理 (personnel) - 用户系统
```
主键: _id (ObjectId)
基本信息:
  - username (string): 用户名
  - password (string): 密码
  - name (string): 真实姓名
  - role (string): 角色类型
  - phone (string): 手机号
  - email (string): 邮箱

患者特有字段:
  - patient_id (string): 患者编号
  - assigned_doctor (string): 分配的医生
  - exercise_plan (string): 关联的锻炼计划

系统字段:
  - created_at (datetime): 创建时间
  - updated_at (datetime): 更新时间
  - is_active (boolean): 是否激活
```

### 3. 锻炼计划 (exercise_plans) - 锻炼管理
```
主键: _id (ObjectId)
计划基本信息:
  - plan_name (string): 计划名称
  - description (string): 计划描述
  - created_by (string): 创建者
  - is_default (boolean): 是否为默认计划

锻炼参数:
  - exercise_weeks (int): 锻炼周数
  - initial_reps (int): 初始次数
  - weekly_increase (int): 每周增加次数
  - max_rest_days_per_week (int): 每周最多休息天数

系统字段:
  - created_at (datetime): 创建时间
  - updated_at (datetime): 更新时间
  - is_active (boolean): 是否激活
```

### 4. 系统信息 (sys_info) - 配置管理
```
主键: _id (ObjectId)
配置项:
  - config_key (string): 配置键
  - config_value (mixed): 配置值
  - description (string): 配置描述
  - category (string): 配置分类

系统字段:
  - created_at (datetime): 创建时间
  - updated_at (datetime): 更新时间
```

### 5. 对话缓存 (reply_cache) - AI对话系统
```
主键: _id (string): "dialogue_cache"
对话模板:
  - question_templates (array): 问题模板数组
    └── 每个模板包含:
        - id (string): 模板ID
        - question_pattern (string): 问题正则表达式
        - reply_templates (array): 回复模板数组
        - tags (array): 标签分类
        - enabled (boolean): 是否启用
        - created_at (datetime): 创建时间

统计信息:
  - stats (object): 使用统计
    - total_hits (int): 总命中次数
    - last_updated (datetime): 最后更新时间
```

### 6. 字符串对话缓存 (reply_cache_str) - 简化对话匹配
```
主键: _id (string): "dialogue_cache_str"
简化模板:
  - question_templates (array): 字符串匹配模板
    └── 每个模板包含:
        - id (string): 模板ID
        - question_pattern (string): 问题字符串
        - reply_templates (array): 回复模板
        - enabled (boolean): 是否启用
        - created_at (datetime): 创建时间
```

## 数据关系模型

```
personnel (用户) 
    ├── 医生 → 管理多个患者
    └── 患者 → 关联一个医生 + 一个锻炼计划

exercise_plans (锻炼计划)
    ├── 由医生创建
    └── 被患者使用

call_records (通话记录)
    ├── 关联患者 (通过患者编号)
    └── 记录锻炼执行情况

sys_info (系统配置)
    └── 全局配置项

reply_cache + reply_cache_str (对话系统)
    └── AI智能对话流程管理
```

## 核心业务流程

1. **用户管理**: personnel 集合管理医生和患者账户
2. **锻炼计划**: exercise_plans 定义锻炼模板，患者关联使用
3. **随访通话**: call_records 记录每次通话和锻炼情况
4. **智能对话**: reply_cache 系列支持AI自动对话
5. **系统配置**: sys_info 管理全局设置

## 数据统计
- 总集合数: 6个
- 总文档数: 182个
- 核心业务数据: call_records (155条通话记录)
- 用户数据: personnel (10个用户)
- 锻炼计划: exercise_plans (8个计划)
