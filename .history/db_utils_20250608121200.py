"""
数据库工具模块：负责与MongoDB数据库的连接和数据操作
https://www.mongodb.com/zh-cn/docs/manual/tutorial/install-mongodb-on-ubuntu/
"""

import motor.motor_asyncio
from datetime import datetime
import json
import re
from typing import Dict, List, Optional, Any
from med_config import MONGODB_CONFIG
from bson import ObjectId
from log_utils import debug, info, warning, error

def process_docid(call_id):
    return call_id

class MongoDBClient:
    def __init__(self, connection_string=None, db_name=None):
        """
        初始化MongoDB客户端
        params:
            connection_string: MongoDB连接字符串
            db_name: 数据库名称
        """
        connection_string = connection_string or MONGODB_CONFIG["connection_string"]
        db_name = db_name or MONGODB_CONFIG["db_name"]
        
        self.client = motor.motor_asyncio.AsyncIOMotorClient(connection_string)
        self.db = self.client[db_name]
        
        # 定义集合
        self.personnel_collection = self.db["personnel"]  # 人员名单集合
        self.call_records_collection = self.db["call_records"]  # 通话记录集合
        self.system_records_collection = self.db["system_records"]
        self.reply_cache_collection = self.db["reply_cache"] # 正则cache
        self.reply_cache_str_collection = self.db["reply_cache_str"] # 纯字符串cache
    
    async def close(self):
        """关闭数据库连接"""
        self.client.close()

    # ================================
    # 人员名单 CRUD 操作
    # ================================
    
    async def add_person(self, person_data: Dict[str, Any]) -> str:
        """
        添加人员信息
        params:
            person_data: 人员数据字典
                {
                    "name": "姓名",
                    "phone": "手机号",
                    "age": 年龄,
                    "gender": "性别",
                    "enrollment_date": "入组时间",
                    "training_status": "训练状态"  # 训练中/未开始/暂停/终止/休息
                }
        returns:
            插入的文档ID
        """
        # 验证必填字段
        required_fields = ["name", "phone", "age", "gender", "enrollment_date", "training_status"]
        for field in required_fields:
            if field not in person_data:
                raise ValueError(f"缺少必填字段: {field}")

        if '_id' not in person_data:
            object_id = ObjectId()
            _id = str(object_id)
            person_data["_id"] = _id
        
        # 添加创建时间
        person_data["created_at"] = datetime.now()
        person_data["updated_at"] = datetime.now()
        
        result = await self.personnel_collection.insert_one(person_data)
        return str(result.inserted_id)
    
    async def get_person_by_phone(self, phone: str) -> Optional[Dict[str, Any]]:
        """
        根据手机号获取人员信息
        params:
            phone: 手机号
        returns:
            人员信息字典或None
        """
        person = await self.personnel_collection.find_one({"phone": phone})
        if person:
            person["_id"] = str(person["_id"])
        return person
    
    async def get_personnel_by_phone(self, phone: str) -> Optional[Dict[str, Any]]:
        """
        根据手机号获取人员信息（别名方法）
        params:
            phone: 手机号
        returns:
            人员信息字典或None
        """
        return await self.get_person_by_phone(phone)
    
    async def get_person_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        根据姓名获取人员信息
        params:
            name: 姓名
        returns:
            人员信息字典或None
        """
        person = await self.personnel_collection.find_one({"name": name})
        if person:
            person["_id"] = str(person["_id"])
        return person
    
    async def update_person_training_status(self, phone: str, status: str) -> bool:
        """
        更新人员训练状态
        params:
            phone: 手机号
            status: 新的训练状态
        returns:
            是否更新成功
        """
        valid_statuses = ["训练中", "未开始", "暂停", "终止", "休息"]
        if status not in valid_statuses:
            raise ValueError(f"无效的训练状态: {status}，有效状态为: {valid_statuses}")
        
        result = await self.personnel_collection.update_one(
            {"phone": phone},
            {
                "$set": {
                    "training_status": status,
                    "updated_at": datetime.now()
                }
            }
        )
        return result.modified_count > 0
    
    async def get_all_personnel(self, training_status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取所有人员信息
        params:
            training_status: 可选，按训练状态过滤
        returns:
            人员信息列表
        """
        query = {}
        if training_status:
            query["training_status"] = training_status
        
        cursor = self.personnel_collection.find(query)
        personnel = []
        async for person in cursor:
            person["_id"] = str(person["_id"])
            personnel.append(person)
        
        return personnel
    
    async def delete_person(self, phone: str) -> bool:
        """
        删除人员信息
        params:
            phone: 手机号
        returns:
            是否删除成功
        """
        result = await self.personnel_collection.delete_one({"phone": phone})
        return result.deleted_count > 0

    # ================================
    # 通话记录 CRUD 操作
    # ================================
    
    async def add_call_record(self, call_data: Dict[str, Any]) -> str:
        """
        添加通话记录
        params:
            call_data: 通话数据字典
                {
                    "患者名字": "姓名",
                    "手机号": "手机号", 
                    "通话时间": "日期时间记录到分钟级",
                    "记录日期": "日期记录到年月日",
                    "拨号状态": "拨号中/已接听/未接听/直接挂断/无法接通/通话结束",
                    "对话历史记录": [],
                    "训练完成情况": "完成或者未完成，未完成的原因",
                    "训练次数": "xx次",
                    "训练时长": "xx分钟",
                    "是否有不适感": "是/否",
                    "不适感内容": "xxx",
                    "是否需要医生人工和患者联系": "是/否",
                    "锻炼辅助仪器是否有问题": "是/否",
                    "锻炼辅助仪器问题内容": "xxxx"
                }
        returns:
            插入的文档ID
        """
        # 验证必填字段
        required_fields = ["患者名字", "手机号", "通话时间", "记录日期"]
        for field in required_fields:
            if field not in call_data:
                raise ValueError(f"缺少必填字段: {field}")

        if '_id' not in call_data:
            object_id = ObjectId()
            _id = str(object_id)
            call_data["_id"] = _id
        
        # 添加创建时间
        call_data["创建时间"] = datetime.now()
        
        # 如果没有提供对话历史，初始化为空列表
        if "对话历史记录" not in call_data:
            call_data["对话历史记录"] = []
        
        result = await self.call_records_collection.insert_one(call_data)
        return str(result.inserted_id)
    
    async def get_call_records_by_phone(self, phone: str, limit: int = None) -> List[Dict[str, Any]]:
        """
        根据手机号获取通话记录
        params:
            phone: 手机号
            limit: 可选，限制返回记录数量
        returns:
            通话记录列表，按时间倒序
        """
        cursor = self.call_records_collection.find({"手机号": phone}).sort("通话时间", -1)
        
        if limit:
            cursor = cursor.limit(limit)
        
        records = []
        async for record in cursor:
            record["_id"] = str(record["_id"])
            records.append(record)
        
        return records
    
    async def get_call_records_by_name(self, name: str, limit: int = None) -> List[Dict[str, Any]]:
        """
        根据姓名获取通话记录
        params:
            name: 患者姓名
            limit: 可选，限制返回记录数量
        returns:
            通话记录列表，按时间倒序
        """
        cursor = self.call_records_collection.find({"患者名字": name}).sort("通话时间", -1)
        
        if limit:
            cursor = cursor.limit(limit)
        
        records = []
        async for record in cursor:
            record["_id"] = str(record["_id"])
            records.append(record)
        
        return records
    
    async def get_call_records_by_date_range(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        根据日期范围获取通话记录
        params:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
        returns:
            通话记录列表
        """
        cursor = self.call_records_collection.find({
            "记录日期": {
                "$gte": start_date,
                "$lte": end_date
            }
        }).sort("通话时间", -1)
        
        records = []
        async for record in cursor:
            record["_id"] = str(record["_id"])
            records.append(record)
        
        return records
    
    async def update_call_record_chat_history(self, record_id: str, chat_history: List[Dict]) -> bool:
        """
        更新通话记录的对话历史
        params:
            record_id: 记录ID
            chat_history: 对话历史列表
        returns:
            是否更新成功
        """

        
        result = await self.call_records_collection.update_one(
            {"_id": process_docid(record_id)},
            {"$set": {"对话历史记录": chat_history}}
        )
        return result.modified_count > 0
    
    async def get_patients_needing_doctor_contact(self) -> List[Dict[str, Any]]:
        """
        获取需要医生人工联系的患者记录
        returns:
            需要医生联系的通话记录列表
        """
        cursor = self.call_records_collection.find({
            "是否需要医生人工和患者联系": "是"
        }).sort("通话时间", -1)
        
        records = []
        async for record in cursor:
            record["_id"] = str(record["_id"])
            records.append(record)
        
        return records
    
    async def get_patients_with_discomfort(self) -> List[Dict[str, Any]]:
        """
        获取有不适感的患者记录
        returns:
            有不适感的通话记录列表
        """
        cursor = self.call_records_collection.find({
            "是否有不适感": "是"
        }).sort("通话时间", -1)
        
        records = []
        async for record in cursor:
            record["_id"] = str(record["_id"])
            records.append(record)
        
        return records
    
    async def get_incomplete_training_records(self) -> List[Dict[str, Any]]:
        """
        获取训练未完成的记录
        returns:
            训练未完成的通话记录列表
        """
        cursor = self.call_records_collection.find({
            "训练完成情况": {"$regex": "未完成"}
        }).sort("通话时间", -1)
        
        records = []
        async for record in cursor:
            record["_id"] = str(record["_id"])
            records.append(record)
        
        return records

    # ================================
    # 统计分析功能
    # ================================
    
    async def get_training_statistics(self) -> Dict[str, Any]:
        """
        获取训练统计信息
        returns:
            统计信息字典
        """
        # 人员状态统计
        personnel_stats = {}
        for status in ["训练中", "未开始", "暂停", "终止", "休息"]:
            count = await self.personnel_collection.count_documents({"training_status": status})
            personnel_stats[status] = count
        
        # 通话记录统计
        total_calls = await self.call_records_collection.count_documents({})
        
        # 今日通话数
        today = datetime.now().strftime("%Y-%m-%d")
        today_calls = await self.call_records_collection.count_documents({"记录日期": today})
        
        # 有不适感的患者数
        discomfort_count = await self.call_records_collection.count_documents({"是否有不适感": "是"})
        
        # 需要医生联系的患者数
        need_doctor_contact = await self.call_records_collection.count_documents({"是否需要医生人工和患者联系": "是"})
        
        return {
            "人员状态统计": personnel_stats,
            "总通话次数": total_calls,
            "今日通话次数": today_calls,
            "有不适感患者数": discomfort_count,
            "需要医生联系患者数": need_doctor_contact
        }

# 创建全局数据库客户端实例
db_client = MongoDBClient()

# 便捷函数
def get_db_client() -> MongoDBClient:
    """获取数据库客户端实例"""
    return db_client


class CurrentRecord:

    def __init__(self):
        self.db = db_client
        self.personnel_collection = self.db.personnel_collection  # 人员名单集合
        self.call_records_collection = self.db.call_records_collection  # 通话记录集合
        self.call_id = None
        self.call_status = None

    async def init_call_record(self, call_data: Dict[str, Any]) -> str:
        """
        添加通话记录
        params:
            call_data: 通话数据字典
                {
                    "患者名字": "姓名",
                    "手机号": "手机号",
                    "通话时间": "日期时间记录到分钟级",
                    "记录日期": "日期记录到年月日",
                    "拨号状态": "拨号中/已接听/未接听/直接挂断/无法接通/通话结束/通话结束-信息解析完成"
                    "对话历史记录": [],
                    "训练完成情况": "完成或者未完成，未完成的原因",
                    "训练次数": "xx次",
                    "训练时长": "xx分钟",
                    "是否有不适感": "是/否",
                    "不适感内容": "xxx",
                    "是否需要医生人工和患者联系": "是/否",
                    "锻炼辅助仪器是否有问题": "是/否",
                    "锻炼辅助仪器问题内容": "xxxx"
                }
        returns:
            插入的文档ID
        """
        # 验证必填字段
        required_fields = ["患者名字", "手机号", "通话时间", "记录日期", "拨号状态"]
        for field in required_fields:
            if field not in call_data:
                raise ValueError(f"缺少必填字段: {field}")

        self.call_status = call_data['拨号状态']

        if '_id' not in call_data:
            object_id = ObjectId()
            _id = str(object_id)
            call_data["_id"] = _id

        # 添加创建时间
        call_data["创建时间"] = datetime.now()

        # 如果没有提供对话历史，初始化为空列表
        if "对话历史记录" not in call_data:
            call_data["对话历史记录"] = []

        result = await self.call_records_collection.insert_one(call_data)
        self.call_id = str(result.inserted_id)
        info(f'插入初始通话记录:{str(call_data)}')
        return self.call_id

    async def update_call_status(self, call_status): #"拨号中/已接听/未接听/直接挂断/无法接通/通话结束"
        result = await self.call_records_collection.update_one(
            {"_id": process_docid(self.call_id)},
            {
                "$set": {
                    "拨号状态": call_status,
                }
            }
        )
        self.call_status = call_status
        return result.modified_count > 0


    async def update_call_record_chat_history(self, chat_history: List[Dict]) -> bool:
        """
        更新通话记录的对话历史
        params:
            record_id: 记录ID
            chat_history: 对话历史列表
        returns:
            是否更新成功
        """

        result = await self.call_records_collection.update_one(
            {"_id": process_docid(self.call_id)},
            {"$set": {"对话历史记录": chat_history}}
        )
        return result.modified_count > 0

    async def update_call_record(self, call_data: Dict[str, Any]) -> bool:
        """
        添加通话记录
        params:
            call_data: 通话数据字典
                {
                    "患者名字": "姓名",
                    "手机号": "手机号",
                    "通话时间": "日期时间记录到分钟级",
                    "记录日期": "日期记录到年月日",
                    "拨号状态": "拨号中/已接听/未接听/直接挂断"
                    "对话历史记录": [],
                    "训练完成情况": "完成或者未完成，未完成的原因",
                    "训练次数": "xx次",
                    "训练时长": "xx分钟",
                    "是否有不适感": "是/否",
                    "不适感内容": "xxx",
                    "是否需要医生人工和患者联系": "是/否",
                    "锻炼辅助仪器是否有问题": "是/否",
                    "锻炼辅助仪器问题内容": "xxxx"
                }
        returns:
            插入的文档ID
        """
        # 验证必填字段
        if '拨号状态' in call_data:
            self.call_status = call_data['拨号状态']

        result = await self.call_records_collection.update_one(
            {"_id": process_docid(self.call_id)},
            {"$set": call_data}
        )
        return result.modified_count > 0