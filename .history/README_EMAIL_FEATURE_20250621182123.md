# 患者报告邮件发送功能

## 功能概述

本功能参照 `send_patient_report_async` 函数，新增了通过邮件发送患者不适感报告的功能。当患者有不适感或需要医生人工联系时，系统会自动向对应的医生发送邮件通知。

## 功能特性

- ✅ 自动查找患者对应的医生邮箱
- ✅ 支持通过患者编号或手机号关联医生
- ✅ 智能邮件主题生成：`患者名字-患者编号-患者手机-不适感内容`
- ✅ 详细的患者训练情况报告
- ✅ 包含患者入组日期、年龄、性别等完整信息
- ✅ 自动从数据库获取患者详细资料
- ✅ 完善的错误处理和日志记录
- ✅ 异步邮件发送，不阻塞主流程

## 核心组件

### 1. DoctorPatientManager 类 (db_utils.py)

新增的医生-患者关联管理类，提供以下功能：

```python
# 根据患者编号获取医生信息
doctor = await doctor_patient_manager.get_doctor_by_patient_id("P0001")

# 根据患者手机号获取医生邮箱
email = await doctor_patient_manager.get_doctor_email_by_patient_phone("18971492577")

# 从通话记录中提取患者信息
patient_info = await doctor_patient_manager.get_patient_info_from_call_record(call_record)
```

### 2. PatientEmailSender 类 (tools/patient_email_sender.py)

专门用于发送患者报告邮件的类：

```python
from tools.patient_email_sender import send_patient_report_email_async

# 发送患者报告邮件
result = await send_patient_report_email_async(patient_data)
```

### 3. 异步邮件发送 (tools/send_email.py)

改进的邮件发送模块，支持异步操作：

```python
from tools.send_email import send_email_async

# 异步发送邮件
result = await send_email_async(subject, body, to_email)
```

## 新增功能：患者详细信息

### 📋 邮件中包含的患者信息

邮件内容现在包含从数据库自动获取的完整患者信息：

- **入组日期** (`enrollment_date`): 患者加入康复训练项目的日期
- **患者年龄** (`age`): 患者年龄
- **患者性别** (`gender`): 患者性别
- **训练状态** (`training_status`): 当前训练状态（训练中/暂停/完成等）
- **分配医生** (`assigned_doctor_name`): 负责该患者的医生姓名

### 🔄 自动数据获取流程

1. **通过手机号查询**: 如果通话记录中没有患者编号，系统会通过手机号查询患者信息
2. **通过患者编号查询**: 如果有患者编号，直接查询对应的患者详细信息
3. **数据增强**: 将查询到的详细信息添加到邮件内容中
4. **容错处理**: 如果某些信息缺失，显示为 "N/A"

## 数据库关联逻辑

### 患者-医生关联关系

```
personnel 集合 (患者记录)
├── patient_id: 患者编号 (如: P0001)
├── assigned_doctor: 分配的医生用户名 (如: doctor01)
├── phone: 患者手机号
└── name: 患者姓名

call_records 集合 (通话记录)
├── 患者编号: 关联到 personnel.patient_id
├── 手机号: 关联到 personnel.phone
└── 其他通话信息...
```

### 医生邮箱映射

由于数据库中没有独立的医生记录，系统使用预配置的医生邮箱映射：

```python
doctor_email_mapping = {
    "doctor01": "<EMAIL>",
    "zjcdoc": "<EMAIL>", 
    "doctor": "<EMAIL>",
    "HUyq": "<EMAIL>"
}
```

## 集成到通话流程

在 `call_manager.py` 中，当检测到患者有不适感或需要医生介入时，系统会同时发送微信和邮件通知：

```python
if ext_res['是否有不适感'] == '是' or ext_res['是否需要医生人工和患者联系'] == '是':
    # 发送微信通知
    result = await send_patient_report_async(ext_res)
    
    # 发送邮件通知
    email_result = await send_patient_report_email_async(ext_res)
```

## 邮件内容格式

### 邮件主题
```
张三-P0001-18971492577-膝盖疼痛严重
```

### 邮件内容
```
患者训练情况报告
==================================================

👤 患者信息
患者姓名：张三
患者编号：P0001
联系电话：18971492577
入组日期：2024-01-15
患者年龄：35
患者性别：男
训练状态：训练中
分配医生：李医生
通话时间：2025-06-21 14:30
拨号状态：通话结束

🏃 训练情况
训练完成情况：未完成 ⚠️
训练次数：50次
训练时长：8分钟

🏥 健康状况
是否有不适感：是 🚨
不适感内容：膝盖疼痛严重
锻炼结束后不适感是否恢复：否

🔧 设备状况
锻炼辅助仪器是否有问题：否 ✅
设备问题详情：正常

👨‍⚕️ 医生介入
是否需要医生人工联系：是 🚨

==================================================
报告生成时间：2025-06-21 17:08:13

此邮件由AI电话随访系统自动生成。
如有疑问，请联系系统管理员。
```

## 使用示例

### 基本使用

```python
import asyncio
from tools.patient_email_sender import send_patient_report_email_async

async def main():
    patient_data = {
        "患者名字": "张三",
        "患者编号": "P0001",
        "手机号": "18971492577",
        "不适感内容": "膝盖疼痛",
        "是否有不适感": "是",
        "是否需要医生人工和患者联系": "是",
        # ... 其他字段
    }
    
    result = await send_patient_report_email_async(patient_data)
    
    if result["success"]:
        print("✅ 邮件发送成功")
        print(f"医生邮箱: {result['details']['doctor_email']}")
    else:
        print(f"❌ 邮件发送失败: {result['error']}")

asyncio.run(main())
```

### 测试功能

```bash
# 运行测试脚本
python test_patient_email.py
```

## 配置说明

### 邮件服务器配置

在 `tools/send_email.py` 中配置邮件服务器：

```python
EMAIL_ADDRESS = "<EMAIL>"  # 发件人邮箱
EMAIL_PASSWORD = "TGHOGOETPSCETEOU"  # 邮箱授权码
SMTP_SERVER = "smtp.163.com"  # SMTP服务器
SMTP_PORT = 465  # SMTP端口
```

### 医生邮箱映射

在 `db_utils.py` 的 `DoctorPatientManager` 类中配置：

```python
def _get_default_doctor_email(self, doctor_username: str) -> str:
    doctor_email_mapping = {
        "doctor01": "<EMAIL>",
        "zjcdoc": "<EMAIL>", 
        "doctor": "<EMAIL>",
        "HUyq": "<EMAIL>"
    }
    return doctor_email_mapping.get(doctor_username, "<EMAIL>")
```

## 错误处理

系统提供完善的错误处理机制：

1. **患者未找到**: 记录警告日志，返回错误信息
2. **医生未分配**: 使用默认邮箱或返回错误
3. **邮件发送失败**: 详细的错误信息和重试机制
4. **网络异常**: 超时处理和异常捕获

## 日志记录

系统会记录详细的操作日志：

```
[INFO] 正在向医生邮箱 <EMAIL> 发送患者 张三 的报告邮件
[INFO] ✅ 患者报告邮件发送成功: 张三 -> <EMAIL>
[WARN] ❌ 邮件发送失败: SMTP认证失败
```

## 扩展建议

1. **邮件模板**: 支持自定义邮件模板
2. **附件支持**: 添加通话录音或图片附件
3. **邮件优先级**: 根据不适感严重程度设置邮件优先级
4. **批量发送**: 支持向多个医生发送邮件
5. **邮件追踪**: 记录邮件发送状态和阅读状态

## 注意事项

1. **邮箱安全**: 确保邮箱授权码的安全性
2. **网络连接**: 确保服务器能够访问SMTP服务器
3. **邮件限制**: 注意邮件服务商的发送频率限制
4. **数据隐私**: 确保患者信息的隐私保护
