#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终测试完整的邮件功能
"""

import asyncio
from datetime import datetime
from tools.patient_email_sender import PatientEmailSender
from log_utils import info


async def test_complete_email_content():
    """测试完整的邮件内容"""
    info("=== 测试完整的邮件内容 ===")
    
    # 模拟完整的通话记录数据
    patient_data = {
        "患者名字": "张三",
        "手机号": "18971492577",
        "通话时间": "2025-06-21 17:30",
        "记录日期": "2025-06-21",
        "拨号状态": "通话结束-信息解析完成",
        "训练完成情况": "完成",
        "训练次数": "100次",
        "训练时长": "30分钟",
        "是否有不适感": "是",
        "不适感内容": "膝盖轻微疼痛",
        "不适感锻炼结束后是否恢复": "是",
        "是否需要医生人工和患者联系": "是",
        "锻炼辅助仪器是否有问题": "否",
        "锻炼辅助仪器问题内容": "设备正常运行"
    }
    
    try:
        sender = PatientEmailSender()
        
        # 获取患者信息
        patient_info = await sender.doctor_manager.get_patient_info_from_call_record(patient_data)
        patient_id = 'N/A'
        
        if patient_info:
            patient_id = patient_info.get('patient_id', 'N/A')
        
        # 创建增强的患者数据
        enhanced_patient_data = patient_data.copy()
        enhanced_patient_data['患者编号'] = patient_id
        
        if patient_info:
            enhanced_patient_data['入组日期'] = patient_info.get('enrollment_date', 'N/A')
            enhanced_patient_data['患者年龄'] = patient_info.get('age', 'N/A')
            enhanced_patient_data['患者性别'] = patient_info.get('gender', 'N/A')
            enhanced_patient_data['训练状态'] = patient_info.get('training_status', 'N/A')
            enhanced_patient_data['分配医生'] = patient_info.get('assigned_doctor_name', 
                                                        patient_info.get('assigned_doctor', 'N/A'))
        
        # 生成邮件内容
        email_content = sender._build_email_content(enhanced_patient_data)
        
        # 生成邮件主题
        patient_name = enhanced_patient_data.get('患者名字', 'N/A')
        patient_phone = enhanced_patient_data.get('手机号', 'N/A')
        discomfort_content = enhanced_patient_data.get('不适感内容', 'N/A')
        subject = f"{patient_name}-{patient_id}-{patient_phone}-{discomfort_content}"
        
        info("📧 完整的邮件信息:")
        info(f"📌 邮件主题: {subject}")
        print("\n" + "📄 邮件内容:")
        print("="*60)
        print(email_content)
        print("="*60)
        
        # 验证关键信息
        checks = [
            ("患者编号", patient_id in email_content and patient_id != 'N/A'),
            ("入组日期", "入组日期：2024-01-15" in email_content),
            ("患者年龄", "患者年龄：35" in email_content),
            ("患者性别", "患者性别：男" in email_content),
            ("训练状态", "训练状态：训练中" in email_content),
            ("分配医生", "分配医生：李医生" in email_content),
            ("训练完成情况", "训练完成情况：完成" in email_content),
            ("不适感内容", "不适感内容：膝盖轻微疼痛" in email_content),
            ("医生介入", "是否需要医生人工联系：是" in email_content)
        ]
        
        info("\n✅ 邮件内容验证结果:")
        for check_name, is_correct in checks:
            status = "✅" if is_correct else "❌"
            info(f"  {status} {check_name}: {'正确' if is_correct else '不正确'}")
        
        # 统计验证结果
        passed_checks = sum(1 for _, is_correct in checks if is_correct)
        total_checks = len(checks)
        info(f"\n📊 验证通过率: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
        
        if passed_checks == total_checks:
            info("🎉 所有验证项目都通过了！邮件功能完全正常！")
        else:
            info("⚠️ 部分验证项目未通过，请检查相关功能。")
            
    except Exception as e:
        info(f"❌ 测试过程中出错: {e}")


async def main():
    """主测试函数"""
    try:
        await test_complete_email_content()
    except Exception as e:
        info(f"测试过程中出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
