"""
主程序：整合所有模块，实现简单的主动拨打电话和连续语音对话功能，支持并发拨号
"""

import asyncio
import uuid
import nats
from med_config import NATS_CONFIG, CALL_CONFIG, LOG_CONFIG, CONCURRENT_CALL_CONFIG
from rpc_utils import RPCUtils
from speech_handler import SpeechHandler
from call_manager import CallManager
from db_utils import db_client
from log_utils import LogLevel, logger, info, error, debug, warning, set_log_level


def init_logging():
    """初始化日志系统"""
    # 根据配置设置日志级别
    level_map = {
        "DEBUG": LogLevel.DEBUG,
        "INFO": LogLevel.INFO,
        "WARNING": LogLevel.WARNING,
        "ERROR": LogLevel.ERROR,
        "CRITICAL": LogLevel.CRITICAL
    }
    
    log_level = level_map.get(LOG_CONFIG.get("level", "INFO"), LogLevel.INFO)
    logger.set_level(log_level)
    
    # 设置其他日志选项
    logger.use_colors = LOG_CONFIG.get("use_colors", True)
    logger.use_icons = LOG_CONFIG.get("use_icons", True)
    logger.show_timestamp = LOG_CONFIG.get("show_timestamp", True)
    logger.show_name = LOG_CONFIG.get("show_name", True)


async def get_personnel_list():
    """从数据库获取人员名单"""
    try:
        info("正在从数据库获取人员名单...")
        
        # 根据配置过滤训练状态
        filter_status = CONCURRENT_CALL_CONFIG.get("filter_training_status")
        if filter_status:
            info("过滤条件：训练状态 = %s", filter_status)
            personnel_list = await db_client.get_all_personnel(training_status=filter_status)
        else:
            personnel_list = await db_client.get_all_personnel()
        
        if not personnel_list:
            warning("数据库中没有找到符合条件的人员记录")
            return []
            
        info("从数据库获取到 %d 条人员记录", len(personnel_list))
        
        # 提取姓名和手机号
        call_list = []
        for person in personnel_list:
            name = person.get("name")
            phone = person.get("phone")
            status = person.get("training_status", "未知")
            if name and phone:
                call_list.append({
                    "name": name, 
                    "phone": phone,
                    "training_status": status
                })
            else:
                warning("人员记录缺少姓名或手机号: %s", person)
        
        info("有效的拨号记录: %d 条", len(call_list))
        return call_list
        
    except Exception as e:
        error("获取人员名单失败: %s", e)
        return []


async def make_single_call(nats_client, person_info, semaphore):
    """单个通话任务"""
    async with semaphore:
        name = person_info["name"]
        phone = person_info["phone"]
        
        info("开始拨打电话: %s (%s)", name, phone)
        
        # 为每个通话创建独立的组件
        ctrl_uuid = str(uuid.uuid4())
        rpc_utils = RPCUtils(nats_client, CALL_CONFIG["service"], ctrl_uuid)
        speech_handler = SpeechHandler(rpc_utils)
        call_manager = CallManager(nats_client, rpc_utils, speech_handler, person_info)
        
        try:
            # 启动通话管理器
            await call_manager.start()
            
            # 拨打电话
            success = await call_manager.make_call(phone)
            
            if success:
                info("成功拨通 %s (%s)", name, phone)
                # 等待通话完成
                await call_manager.wait_for_call_completion()
                info("通话结束: %s (%s)", name, phone)
            else:
                warning("拨号失败: %s (%s)", name, phone)
                
        except Exception as e:
            error("通话过程出错 %s (%s): %s", name, phone, e)
            
        finally:
            # 清理资源
            call_manager.stop()
            await call_manager.cleanup()
            
        # 通话间隔
        if CONCURRENT_CALL_CONFIG.get("call_interval", 0) > 0:
            await asyncio.sleep(CONCURRENT_CALL_CONFIG["call_interval"])


def split_into_batches(personnel_list, batch_size):
    """将人员名单分割成批次"""
    for i in range(0, len(personnel_list), batch_size):
        yield personnel_list[i:i + batch_size]


async def process_batch(batch, batch_num, total_batches):
    """处理一个批次的拨号"""
    info("=== 开始处理第 %d/%d 批次，共 %d 个电话 ===", batch_num, total_batches, len(batch))
    
    max_concurrent = CONCURRENT_CALL_CONFIG.get("max_concurrent_calls", 3)
    semaphore = asyncio.Semaphore(max_concurrent)
    
    nats_client = None
    try:
        # 连接NATS服务器
        nats_client = await nats.connect(NATS_CONFIG["url"])
        
        # 创建批次内的通话任务
        tasks = []
        for person_info in batch:
            task = make_single_call(nats_client, person_info, semaphore)
            tasks.append(task)
        
        # 并发执行批次内的任务
        await asyncio.gather(*tasks, return_exceptions=True)
        info("=== 第 %d/%d 批次处理完成 ===", batch_num, total_batches)
        
    except Exception as e:
        error("批次 %d 处理出错: %s", batch_num, e)
        
    finally:
        if nats_client:
            await nats_client.close()


async def concurrent_calls(personnel_list):
    """并发拨打电话"""
    if not personnel_list:
        warning("人员名单为空，无法进行拨号")
        return
        
    max_concurrent = CONCURRENT_CALL_CONFIG.get("max_concurrent_calls", 3)
    batch_size = CONCURRENT_CALL_CONFIG.get("batch_size", 10)
    batch_interval = CONCURRENT_CALL_CONFIG.get("batch_interval", 30)
    
    info("=== 并发拨号配置 ===")
    info("最大并发数: %d", max_concurrent)
    info("批次大小: %d", batch_size)
    info("批次间隔: %d 秒", batch_interval)
    info("总共需要拨打 %d 个电话", len(personnel_list))
    
    # 分割成批次
    batches = list(split_into_batches(personnel_list, batch_size))
    total_batches = len(batches)
    info("分为 %d 个批次进行处理", total_batches)
    
    try:
        for batch_num, batch in enumerate(batches, 1):
            await process_batch(batch, batch_num, total_batches)
            
            # 如果不是最后一个批次，等待批次间隔
            if batch_num < total_batches and batch_interval > 0:
                info("等待 %d 秒后开始下一批次...", batch_interval)
                await asyncio.sleep(batch_interval)
        
        info("🎉 所有拨号任务已完成！")
        
    except Exception as e:
        error("并发拨号过程出错: %s", e)


async def main():
    """主函数"""
    info("=== AI电话机器人 - 支持并发拨号 ===")
    
    try:
        # 从数据库获取人员名单
        personnel_list = await get_personnel_list()
        
        if not personnel_list:
            error("无法获取人员名单，程序退出")
            return

        # ignores = ['17764016802']
        # personnel_list = [p for p in personnel_list if p['phone'] not in ignores]

        # for p in personnel_list:
        #     if p['phone'] == '17764016802':
        #         p['phone'] = '13808625556'

        # 显示拨号计划
        personnel_list_new = []
        info("=== 拨号计划 只拨打训练中的 ===")

        for i, person in enumerate(personnel_list, 1):
            if str(person["training_status"]).strip() == '训练中':
                info("%d. %s (%s) - %s", i, person["name"], person["phone"], person["training_status"])
                personnel_list_new.append(person)
        info("=== 计划结束 ===")

        personnel_list = personnel_list_new
        
        # 开始并发拨号
        await concurrent_calls(personnel_list)
        
    except KeyboardInterrupt:
        info("\n收到中断信号，正在关闭...")
        
    except Exception as e:
        error("程序运行出错: %s", e)
        
    finally:
        # 关闭数据库连接
        await db_client.close()
        info("程序已退出")



def show_config():
    """测试配置是否正确"""
    info("=== 配置测试 ===")
    info("NATS URL: %s", NATS_CONFIG['url'])
    info("服务名称: %s", CALL_CONFIG['service'])
    info("拨号字符串模板: %s", CALL_CONFIG['dial_string'])
    info("最大并发数: %d", CONCURRENT_CALL_CONFIG['max_concurrent_calls'])
    info("批次大小: %d", CONCURRENT_CALL_CONFIG['batch_size'])
    info("配置检查完成")


if __name__ == "__main__":
    # 初始化日志系统
    init_logging()
    
    # 可以先测试配置
    # show_config()
    
    # 可以先添加测试数据（取消注释以添加测试数据）

    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        warning("\n程序被用户中断")
    except Exception as e:
        error("程序启动失败: %s", e) 