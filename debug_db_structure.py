#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试数据库结构
"""

import asyncio
from db_utils import db_client
from log_utils import info


async def debug_personnel_structure():
    """调试人员数据结构"""
    info("=== 调试人员数据结构 ===")
    
    try:
        # 获取所有人员记录
        personnel = await db_client.get_all_personnel()
        info(f"数据库中共有 {len(personnel)} 个人员记录")
        
        if personnel:
            info("第一条记录的完整结构:")
            first_record = personnel[0]
            for key, value in first_record.items():
                info(f"  {key}: {value} ({type(value).__name__})")
            
            info("\n所有记录的关键字段:")
            for i, person in enumerate(personnel):
                name = person.get('name', 'N/A')
                phone = person.get('phone', 'N/A')
                role = person.get('role', 'N/A')
                patient_id = person.get('patient_id', 'N/A')
                assigned_doctor = person.get('assigned_doctor', 'N/A')
                email = person.get('email', 'N/A')
                training_status = person.get('training_status', 'N/A')
                
                info(f"  {i+1}. {name} (phone: {phone})")
                info(f"     role: {role}, patient_id: {patient_id}")
                info(f"     assigned_doctor: {assigned_doctor}, email: {email}")
                info(f"     training_status: {training_status}")
                info("")
                
    except Exception as e:
        info(f"调试人员数据结构时出错: {e}")


async def debug_call_records_structure():
    """调试通话记录结构"""
    info("=== 调试通话记录结构 ===")
    
    try:
        # 获取需要医生联系的记录
        records = await db_client.get_patients_needing_doctor_contact()
        info(f"需要医生联系的记录数: {len(records)}")
        
        if records:
            info("第一条通话记录的完整结构:")
            first_record = records[0]
            for key, value in first_record.items():
                if key != '对话历史记录':  # 跳过对话历史，太长了
                    info(f"  {key}: {value} ({type(value).__name__})")
                else:
                    info(f"  {key}: [对话历史记录，共{len(value) if value else 0}条]")
                    
    except Exception as e:
        info(f"调试通话记录结构时出错: {e}")


async def main():
    """主函数"""
    try:
        await debug_personnel_structure()
        print("\n" + "="*60 + "\n")
        await debug_call_records_structure()
        
    except Exception as e:
        info(f"调试过程中出错: {e}")
    finally:
        await db_client.close()
        info("数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
