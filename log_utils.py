"""
日志工具模块
提供统一的日志管理功能，支持不同级别的日志输出
"""

import sys
import datetime
from enum import Enum
from typing import Any, Optional


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = 0
    INFO = 1
    WARNING = 2
    ERROR = 3
    CRITICAL = 4


class Logger:
    """日志管理器类"""
    
    # ANSI颜色代码
    COLORS = {
        LogLevel.DEBUG: '\033[36m',      # 青色
        LogLevel.INFO: '\033[32m',       # 绿色
        LogLevel.WARNING: '\033[33m',    # 黄色
        LogLevel.ERROR: '\033[31m',      # 红色
        LogLevel.CRITICAL: '\033[35m',   # 紫色
    }
    
    RESET = '\033[0m'  # 重置颜色
    
    # 日志级别对应的标签
    LEVEL_TAGS = {
        LogLevel.DEBUG: '[DEBUG]',
        LogLevel.INFO: '[INFO] ',
        LogLevel.WARNING: '[WARN] ',
        LogLevel.ERROR: '[ERROR]',
        LogLevel.CRITICAL: '[CRIT] ',
    }
    
    # 日志级别对应的图标
    LEVEL_ICONS = {
        LogLevel.DEBUG: '🔍',
        LogLevel.INFO: 'ℹ️ ',
        LogLevel.WARNING: '⚠️ ',
        LogLevel.ERROR: '❌',
        LogLevel.CRITICAL: '💥',
    }
    
    def __init__(self, name: str = "Logger", level: LogLevel = LogLevel.INFO, 
                 use_colors: bool = True, use_icons: bool = True, 
                 show_timestamp: bool = True, show_name: bool = True):
        """
        初始化日志器
        
        Args:
            name: 日志器名称
            level: 最小日志级别
            use_colors: 是否使用颜色输出
            use_icons: 是否使用图标
            show_timestamp: 是否显示时间戳
            show_name: 是否显示日志器名称
        """
        self.name = name
        self.level = level
        self.use_colors = use_colors
        self.use_icons = use_icons
        self.show_timestamp = show_timestamp
        self.show_name = show_name
    
    def _format_message(self, level: LogLevel, message: str, *args) -> str:
        """格式化日志消息"""
        # 处理格式化参数
        if args:
            try:
                formatted_message = message % args
            except (TypeError, ValueError):
                formatted_message = f"{message} {' '.join(map(str, args))}"
        else:
            formatted_message = str(message)
        
        # 构建日志行
        parts = []
        
        # 时间戳
        if self.show_timestamp:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            parts.append(f"[{timestamp}]")
        
        # 日志器名称
        if self.show_name:
            parts.append(f"[{self.name}]")
        
        # 日志级别标签
        level_tag = self.LEVEL_TAGS[level]
        
        # 添加颜色和图标
        if self.use_colors:
            color = self.COLORS[level]
            if self.use_icons:
                icon = self.LEVEL_ICONS[level]
                level_display = f"{color}{icon}{level_tag}{self.RESET}"
            else:
                level_display = f"{color}{level_tag}{self.RESET}"
        else:
            if self.use_icons:
                icon = self.LEVEL_ICONS[level]
                level_display = f"{icon}{level_tag}"
            else:
                level_display = level_tag
        
        parts.append(level_display)
        parts.append(formatted_message)
        
        return " ".join(parts)
    
    def _log(self, level: LogLevel, message: Any, *args, **kwargs):
        """内部日志方法"""
        if level.value >= self.level.value:
            formatted = self._format_message(level, str(message), *args)
            
            # 根据日志级别选择输出流
            output = sys.stderr if level.value >= LogLevel.ERROR.value else sys.stdout
            
            print(formatted, file=output)
            
            # 如果有额外的关键字参数，也打印出来
            if kwargs:
                extra_info = " ".join(f"{k}={v}" for k, v in kwargs.items())
                extra_formatted = self._format_message(level, f"  └─ {extra_info}")
                print(extra_formatted, file=output)
    
    def debug(self, message: Any, *args, **kwargs):
        """调试日志"""
        self._log(LogLevel.DEBUG, message, *args, **kwargs)
    
    def info(self, message: Any, *args, **kwargs):
        """信息日志"""
        self._log(LogLevel.INFO, message, *args, **kwargs)
    
    def warning(self, message: Any, *args, **kwargs):
        """警告日志"""
        self._log(LogLevel.WARNING, message, *args, **kwargs)
    
    def warn(self, message: Any, *args, **kwargs):
        """警告日志的简短别名"""
        self.warning(message, *args, **kwargs)
    
    def error(self, message: Any, *args, **kwargs):
        """错误日志"""
        self._log(LogLevel.ERROR, message, *args, **kwargs)
    
    def critical(self, message: Any, *args, **kwargs):
        """严重错误日志"""
        self._log(LogLevel.CRITICAL, message, *args, **kwargs)
    
    def set_level(self, level: LogLevel):
        """设置日志级别"""
        self.level = level
    
    def get_level(self) -> LogLevel:
        """获取当前日志级别"""
        return self.level


# 创建默认的全局日志器实例
logger = Logger(name="SuiFangMedCall", level=LogLevel.DEBUG)

# 提供便捷的全局日志方法
def debug(message: Any, *args, **kwargs):
    """全局调试日志方法"""
    logger.debug(message, *args, **kwargs)

def info(message: Any, *args, **kwargs):
    """全局信息日志方法"""
    logger.info(message, *args, **kwargs)

def warning(message: Any, *args, **kwargs):
    """全局警告日志方法"""
    logger.warning(message, *args, **kwargs)

def warn(message: Any, *args, **kwargs):
    """全局警告日志方法（简短别名）"""
    logger.warning(message, *args, **kwargs)

def error(message: Any, *args, **kwargs):
    """全局错误日志方法"""
    logger.error(message, *args, **kwargs)

def critical(message: Any, *args, **kwargs):
    """全局严重错误日志方法"""
    logger.critical(message, *args, **kwargs)

def set_log_level(level: LogLevel):
    """设置全局日志级别"""
    logger.set_level(level)

def get_log_level() -> LogLevel:
    """获取全局日志级别"""
    return logger.get_level()

def filter_print_xcc_events(json_message, events=[]):
    for event in events:
        if "method" in json_message and json_message["method"] == event:
            return True

    return False



if __name__ == "__main__":
    # 测试代码
    print("=== 日志系统测试 ===")
    
    # 测试不同级别的日志
    debug("这是调试信息")
    info("这是信息日志")
    warning("这是警告信息")
    error("这是错误信息")
    critical("这是严重错误")
    
    print("\n=== 设置日志级别为WARNING ===")
    set_log_level(LogLevel.WARNING)
    
    debug("这条调试信息不会显示")
    info("这条信息日志不会显示")
    warning("这条警告信息会显示")
    error("这条错误信息会显示")
    
    print("\n=== 带参数的日志 ===")
    info("用户%s登录成功，IP地址：%s", "张三", "***********")
    error("连接失败，重试次数：%d", 3, timeout=30, host="localhost")
