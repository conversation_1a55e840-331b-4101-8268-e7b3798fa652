"""
系统信息管理客户端：负责与MongoDB数据库中的系统信息进行CRUD操作
"""

import motor.motor_asyncio
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Any, Union
from bson import ObjectId
import json
import hashlib
import bcrypt
from enum import Enum


class UserRole(Enum):
    """用户权限枚举"""
    ADMIN = "管理员"
    DOCTOR = "医生"


class SystemStatus(Enum):
    """系统状态枚举"""
    RUNNING = "运行中"
    STOPPED = "已停止"
    MAINTENANCE = "维护中"
    ERROR = "异常"


class SystemClient:
    """系统信息管理客户端"""
    
    def __init__(self, connection_string=None, db_name=None):
        """
        初始化系统信息管理客户端
        
        Args:
            connection_string: MongoDB连接字符串，默认从config.py读取
            db_name: 数据库名称，默认从config.py读取
        """
        try:
            from med_config import MONGODB_CONFIG
            connection_string = connection_string or MONGODB_CONFIG["connection_string"]
            db_name = db_name or MONGODB_CONFIG["db_name"]
        except ImportError:
            # 如果无法导入config，使用默认值
            connection_string = connection_string or "mongodb://localhost:27017"
            db_name = db_name or "med_call_records"
        
        self.client = motor.motor_asyncio.AsyncIOMotorClient(connection_string)
        self.db = self.client[db_name]
        
        # 系统信息集合
        self.sys_info_collection = self.db["sys_info"]
    
    async def close(self):
        """关闭数据库连接"""
        self.client.close()

    # ================================
    # 系统基础信息管理
    # ================================
    
    async def init_system_info(self) -> bool:
        """
        初始化系统信息，创建默认的系统配置文档
        
        Returns:
            bool: 是否初始化成功
        """
        try:
            # 检查是否已经存在系统信息
            existing = await self.sys_info_collection.find_one({"doc_type": "system_config"})
            if existing:
                print("系统信息已存在，无需重复初始化")
                return True
            
            # 创建默认系统配置
            default_config = {
                "doc_type": "system_config",
                "system_name": "医疗随访AI电话系统",
                "version": "1.0.0",
                "status": SystemStatus.STOPPED.value,
                "start_time": None,
                "running_duration": 0,  # 运行时长（秒）
                "last_status_update": datetime.now(),
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                
                # 电话拨打时间段配置
                "call_time_slots": [
                    {
                        "name": "上午时段",
                        "start_time": "09:00",
                        "end_time": "12:00",
                        "enabled": True
                    },
                    {
                        "name": "下午时段", 
                        "start_time": "14:00",
                        "end_time": "17:30",
                        "enabled": True
                    },
                    {
                        "name": "晚上时段",
                        "start_time": "18:00", 
                        "end_time": "20:00",
                        "enabled": False
                    }
                ],
                
                # 系统运行配置
                "max_concurrent_calls": 3,
                "retry_times": 3,
                "retry_interval": 5,
                "call_interval": 2,
                "batch_size": 10,
                "batch_interval": 30,
                "skip_weekends": False,
                "max_calls_per_day": None,
                
                # 系统统计信息
                "total_calls_today": 0,
                "total_calls_all": 0,
                "last_call_time": None,
                "system_errors": []
            }
            
            result = await self.sys_info_collection.insert_one(default_config)
            print(f"系统信息初始化成功，ID: {result.inserted_id}")
            return True
            
        except Exception as e:
            print(f"系统信息初始化失败: {e}")
            return False

    async def get_system_info(self) -> Optional[Dict[str, Any]]:
        """
        获取系统基础信息
        
        Returns:
            Dict: 系统信息字典或None
        """
        try:
            info = await self.sys_info_collection.find_one({"doc_type": "system_config"})
            if info:
                info["_id"] = str(info["_id"])
                # 如果系统正在运行，计算实时运行时长
                if info.get("status") == SystemStatus.RUNNING.value and info.get("start_time"):
                    start_time = info["start_time"]
                    current_duration = (datetime.now() - start_time).total_seconds()
                    info["current_running_duration"] = int(current_duration)
                
            return info
        except Exception as e:
            print(f"获取系统信息失败: {e}")
            return None

    async def update_system_status(self, status: Union[SystemStatus, str], 
                                   error_message: str = None) -> bool:
        """
        更新系统运行状态
        
        Args:
            status: 系统状态
            error_message: 错误信息（状态为ERROR时使用）
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if isinstance(status, SystemStatus):
                status = status.value
            
            update_data = {
                "status": status,
                "last_status_update": datetime.now(),
                "updated_at": datetime.now()
            }
            
            # 如果状态变为运行中，记录启动时间
            if status == SystemStatus.RUNNING.value:
                update_data["start_time"] = datetime.now()
                update_data["running_duration"] = 0
            
            # 如果状态变为停止，计算总运行时长
            elif status == SystemStatus.STOPPED.value:
                current_info = await self.get_system_info()
                if current_info and current_info.get("start_time"):
                    start_time = current_info["start_time"]
                    if isinstance(start_time, str):
                        start_time = datetime.fromisoformat(start_time)
                    total_duration = (datetime.now() - start_time).total_seconds()
                    update_data["running_duration"] = int(total_duration)
                    update_data["start_time"] = None
            
            # 如果是错误状态，记录错误信息
            if status == SystemStatus.ERROR.value and error_message:
                error_record = {
                    "timestamp": datetime.now(),
                    "message": error_message
                }
                await self.sys_info_collection.update_one(
                    {"doc_type": "system_config"},
                    {"$push": {"system_errors": error_record}}
                )
            
            result = await self.sys_info_collection.update_one(
                {"doc_type": "system_config"},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
        except Exception as e:
            print(f"更新系统状态失败: {e}")
            return False

    # ================================
    # 用户账号管理
    # ================================
    
    def _hash_password(self, password: str) -> str:
        """加密密码"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def _verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    async def create_user(self, username: str, password: str, role: Union[UserRole, str],
                         full_name: str = None, email: str = None, phone: str = None) -> str:
        """
        创建用户账号
        
        Args:
            username: 用户名
            password: 密码
            role: 用户权限
            full_name: 真实姓名
            email: 邮箱
            phone: 电话
            
        Returns:
            str: 用户ID
        """
        try:
            # 检查用户名是否已存在
            existing = await self.sys_info_collection.find_one({
                "doc_type": "user_account",
                "username": username
            })
            if existing:
                raise ValueError(f"用户名 {username} 已存在")
            
            if isinstance(role, UserRole):
                role = role.value
            
            # 验证权限类型
            if role not in [UserRole.ADMIN.value, UserRole.DOCTOR.value]:
                raise ValueError(f"无效的用户权限: {role}")
            
            user_data = {
                "doc_type": "user_account",
                "username": username,
                "password_hash": self._hash_password(password),
                "role": role,
                "full_name": full_name or username,
                "email": email,
                "phone": phone,
                "is_active": True,
                "last_login": None,
                "login_count": 0,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            result = await self.sys_info_collection.insert_one(user_data)
            return str(result.inserted_id)
            
        except Exception as e:
            print(f"创建用户失败: {e}")
            raise

    async def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Dict: 用户信息或None
        """
        try:
            user = await self.sys_info_collection.find_one({
                "doc_type": "user_account",
                "username": username,
                "is_active": True
            })
            
            if not user:
                return None
            
            if not self._verify_password(password, user["password_hash"]):
                return None
            
            # 更新登录信息
            await self.sys_info_collection.update_one(
                {"_id": user["_id"]},
                {
                    "$set": {
                        "last_login": datetime.now(),
                        "updated_at": datetime.now()
                    },
                    "$inc": {"login_count": 1}
                }
            )
            
            # 返回用户信息（不包含密码）
            user["_id"] = str(user["_id"])
            del user["password_hash"]
            return user
            
        except Exception as e:
            print(f"用户认证失败: {e}")
            return None

    async def get_all_users(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """
        获取所有用户
        
        Args:
            include_inactive: 是否包含非活跃用户
            
        Returns:
            List: 用户列表
        """
        try:
            query = {"doc_type": "user_account"}
            if not include_inactive:
                query["is_active"] = True
            
            cursor = self.sys_info_collection.find(query)
            users = []
            async for user in cursor:
                user["_id"] = str(user["_id"])
                del user["password_hash"]  # 不返回密码
                users.append(user)
            
            return users
        except Exception as e:
            print(f"获取用户列表失败: {e}")
            return []

    async def update_user_password(self, username: str, new_password: str) -> bool:
        """
        更新用户密码
        
        Args:
            username: 用户名
            new_password: 新密码
            
        Returns:
            bool: 是否更新成功
        """
        try:
            result = await self.sys_info_collection.update_one(
                {"doc_type": "user_account", "username": username},
                {"$set": {
                    "password_hash": self._hash_password(new_password),
                    "updated_at": datetime.now()
                }}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"更新密码失败: {e}")
            return False

    async def deactivate_user(self, username: str) -> bool:
        """
        停用用户账号
        
        Args:
            username: 用户名
            
        Returns:
            bool: 是否操作成功
        """
        try:
            result = await self.sys_info_collection.update_one(
                {"doc_type": "user_account", "username": username},
                {"$set": {
                    "is_active": False,
                    "updated_at": datetime.now()
                }}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"停用用户失败: {e}")
            return False

    # ================================
    # 电话拨打时间段管理
    # ================================
    
    async def get_call_time_slots(self) -> List[Dict[str, Any]]:
        """
        获取电话拨打时间段配置
        
        Returns:
            List: 时间段列表
        """
        try:
            info = await self.get_system_info()
            return info.get("call_time_slots", []) if info else []
        except Exception as e:
            print(f"获取时间段配置失败: {e}")
            return []

    async def update_call_time_slots(self, time_slots: List[Dict[str, Any]]) -> bool:
        """
        更新电话拨打时间段配置
        
        Args:
            time_slots: 时间段列表
                [{
                    "name": "时段名称",
                    "start_time": "09:00",
                    "end_time": "12:00", 
                    "enabled": True
                }]
                
        Returns:
            bool: 是否更新成功
        """
        try:
            # 验证时间格式
            for slot in time_slots:
                if "start_time" in slot:
                    time.fromisoformat(slot["start_time"])
                if "end_time" in slot:
                    time.fromisoformat(slot["end_time"])
            
            result = await self.sys_info_collection.update_one(
                {"doc_type": "system_config"},
                {"$set": {
                    "call_time_slots": time_slots,
                    "updated_at": datetime.now()
                }}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"更新时间段配置失败: {e}")
            return False

    async def is_in_call_time(self) -> bool:
        """
        检查当前时间是否在拨打时间段内
        
        Returns:
            bool: 是否在拨打时间内
        """
        try:
            time_slots = await self.get_call_time_slots()
            current_time = datetime.now().time()
            
            for slot in time_slots:
                if not slot.get("enabled", True):
                    continue
                
                start_time = time.fromisoformat(slot["start_time"])
                end_time = time.fromisoformat(slot["end_time"])
                
                if start_time <= current_time <= end_time:
                    return True
            
            return False
        except Exception as e:
            print(f"检查拨打时间失败: {e}")
            return False

    # ================================
    # 系统配置管理
    # ================================
    
    async def update_system_config(self, config_updates: Dict[str, Any]) -> bool:
        """
        更新系统配置
        
        Args:
            config_updates: 要更新的配置项
            
        Returns:
            bool: 是否更新成功
        """
        try:
            config_updates["updated_at"] = datetime.now()
            
            result = await self.sys_info_collection.update_one(
                {"doc_type": "system_config"},
                {"$set": config_updates}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"更新系统配置失败: {e}")
            return False

    async def get_system_config(self, keys: List[str] = None) -> Dict[str, Any]:
        """
        获取系统配置
        
        Args:
            keys: 要获取的配置键列表，None表示获取所有
            
        Returns:
            Dict: 配置信息
        """
        try:
            info = await self.get_system_info()
            if not info:
                return {}
            
            if keys:
                return {key: info.get(key) for key in keys if key in info}
            else:
                return info
        except Exception as e:
            print(f"获取系统配置失败: {e}")
            return {}

    # ================================
    # 系统统计信息
    # ================================
    
    async def update_call_statistics(self, calls_today: int = None, calls_total: int = None) -> bool:
        """
        更新通话统计信息
        
        Args:
            calls_today: 今日通话数
            calls_total: 总通话数
            
        Returns:
            bool: 是否更新成功
        """
        try:
            update_data = {
                "last_call_time": datetime.now(),
                "updated_at": datetime.now()
            }
            
            if calls_today is not None:
                update_data["total_calls_today"] = calls_today
            if calls_total is not None:
                update_data["total_calls_all"] = calls_total
            
            result = await self.sys_info_collection.update_one(
                {"doc_type": "system_config"},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"更新统计信息失败: {e}")
            return False

    async def get_system_statistics(self) -> Dict[str, Any]:
        """
        获取系统统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            info = await self.get_system_info()
            if not info:
                return {}
            
            stats = {
                "system_status": info.get("status"),
                "running_duration": info.get("running_duration", 0),
                "current_running_duration": info.get("current_running_duration", 0),
                "total_calls_today": info.get("total_calls_today", 0),
                "total_calls_all": info.get("total_calls_all", 0),
                "last_call_time": info.get("last_call_time"),
                "error_count": len(info.get("system_errors", [])),
                "start_time": info.get("start_time"),
                "last_status_update": info.get("last_status_update")
            }
            
            return stats
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}

    # ================================
    # 系统维护功能
    # ================================
    
    async def clear_system_errors(self) -> bool:
        """
        清空系统错误记录
        
        Returns:
            bool: 是否清空成功
        """
        try:
            result = await self.sys_info_collection.update_one(
                {"doc_type": "system_config"},
                {"$set": {
                    "system_errors": [],
                    "updated_at": datetime.now()
                }}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"清空错误记录失败: {e}")
            return False

    async def get_system_errors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取系统错误记录
        
        Args:
            limit: 限制返回数量
            
        Returns:
            List: 错误记录列表
        """
        try:
            info = await self.get_system_info()
            if not info:
                return []
            
            errors = info.get("system_errors", [])
            # 按时间排序，最新的在前
            errors.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)
            
            return errors[:limit]
        except Exception as e:
            print(f"获取错误记录失败: {e}")
            return []

    async def backup_system_data(self) -> Dict[str, Any]:
        """
        备份系统数据
        
        Returns:
            Dict: 备份数据
        """
        try:
            cursor = self.sys_info_collection.find({"doc_type": {"$in": ["system_config", "user_account"]}})
            backup_data = {
                "backup_time": datetime.now(),
                "data": []
            }
            
            async for doc in cursor:
                doc["_id"] = str(doc["_id"])
                backup_data["data"].append(doc)
            
            return backup_data
        except Exception as e:
            print(f"备份系统数据失败: {e}")
            return {}


# 创建全局系统客户端实例
system_client = SystemClient()


# 便捷函数
def get_system_client() -> SystemClient:
    """获取系统客户端实例"""
    return system_client


# ================================
# 使用示例和测试函数
# ================================

async def test_system_client():
    """测试系统客户端功能"""
    client = SystemClient()
    
    print("=" * 60)
    print("系统客户端功能测试")
    print("=" * 60)
    
    try:
        # 1. 初始化系统信息
        print("\n1. 初始化系统信息...")
        await client.init_system_info()
        
        # 2. 创建管理员账号
        print("\n2. 创建管理员账号...")
        try:
            admin_id = await client.create_user(
                username="admin",
                password="@147258369",
                role=UserRole.ADMIN,
                full_name="系统管理员",
                email="<EMAIL>"
            )
            print(f"管理员账号创建成功，ID: {admin_id}")
        except ValueError as e:
            print(f"管理员账号可能已存在: {e}")
        
        # 3. 创建医生账号
        print("\n3. 创建医生账号...")
        try:
            doctor_id = await client.create_user(
                username="doctor01",
                password="doctor123",
                role=UserRole.DOCTOR,
                full_name="李医生",
                email="<EMAIL>",
                phone="13800138001"
            )
            print(f"医生账号创建成功，ID: {doctor_id}")
        except ValueError as e:
            print(f"医生账号可能已存在: {e}")
        
        # 4. 测试用户认证
        print("\n4. 测试用户认证...")
        user = await client.authenticate_user("admin", "admin123")
        if user:
            print(f"认证成功: {user['username']} ({user['role']})")
        else:
            print("认证失败")
        
        # 5. 获取系统信息
        print("\n5. 获取系统信息...")
        sys_info = await client.get_system_info()
        if sys_info:
            print(f"系统名称: {sys_info['system_name']}")
            print(f"系统版本: {sys_info['version']}")
            print(f"系统状态: {sys_info['status']}")
        
        # 6. 更新系统状态
        print("\n6. 更新系统状态...")
        await client.update_system_status(SystemStatus.RUNNING)
        print("系统状态已更新为运行中")
        
        # 7. 获取时间段配置
        print("\n7. 获取时间段配置...")
        time_slots = await client.get_call_time_slots()
        for slot in time_slots:
            print(f"  {slot['name']}: {slot['start_time']}-{slot['end_time']} (启用: {slot['enabled']})")
        
        # 8. 检查当前是否在拨打时间内
        print("\n8. 检查拨打时间...")
        in_time = await client.is_in_call_time()
        print(f"当前是否可以拨打电话: {in_time}")
        
        # 9. 获取统计信息
        print("\n9. 获取统计信息...")
        stats = await client.get_system_statistics()
        print(f"系统状态: {stats.get('system_status')}")
        print(f"运行时长: {stats.get('current_running_duration', 0)} 秒")
        print(f"今日通话数: {stats.get('total_calls_today', 0)}")
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    finally:
        await client.close()


if __name__ == "__main__":
    """直接运行此文件进行功能测试"""
    import asyncio
    
    print("🚀 启动系统客户端测试...")
    print("⚠️  注意：请确保MongoDB服务已启动")
    
    asyncio.run(test_system_client()) 