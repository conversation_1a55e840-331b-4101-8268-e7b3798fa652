# SystemClient 系统信息管理模块

## 概述

`SystemClient` 是医疗随访AI电话系统的核心系统信息管理模块，提供对系统配置、用户账号、运行状态、统计信息等的完整CRUD操作。该模块将原本分散在 `config.py` 中的静态配置迁移到数据库中，实现动态配置管理。

## 主要功能

### 🔧 系统基础信息管理
- 系统初始化和配置
- 系统状态管理（运行中/已停止/维护中/异常）
- 系统运行时长统计
- 系统错误记录和监控

### 👤 用户账号管理
- 用户创建（管理员/医生两种权限）
- 密码加密存储（使用bcrypt）
- 用户认证和登录
- 用户信息查询和管理
- 账号激活/停用

### ⏰ 电话拨打时间段管理
- 动态配置拨打时间段
- 启用/禁用特定时间段
- 实时检查当前时间是否可拨打
- 替代原config.py中的静态时间配置

### 📊 系统配置管理
- 并发通话数量配置
- 批次处理配置
- 重试机制配置
- 其他系统运行参数

### 📈 统计信息管理
- 今日通话数统计
- 总通话数统计
- 系统错误统计
- 运行时长统计

### 🔧 系统维护功能
- 数据备份
- 错误记录清理
- 系统健康检查

## 数据库结构

### sys_info 集合

该模块在 MongoDB 中创建 `sys_info` 集合，包含两种类型的文档：

#### 1. 系统配置文档 (doc_type: "system_config")
```json
{
  "doc_type": "system_config",
  "system_name": "医疗随访AI电话系统",
  "version": "1.0.0",
  "status": "运行中",
  "start_time": "2024-01-20T10:00:00Z",
  "running_duration": 3600,
  "call_time_slots": [
    {
      "name": "上午时段",
      "start_time": "09:00",
      "end_time": "12:00",
      "enabled": true
    }
  ],
  "max_concurrent_calls": 3,
  "total_calls_today": 25,
  "system_errors": []
}
```

#### 2. 用户账号文档 (doc_type: "user_account")
```json
{
  "doc_type": "user_account",
  "username": "admin",
  "password_hash": "...",
  "role": "管理员",
  "full_name": "系统管理员",
  "email": "<EMAIL>",
  "is_active": true,
  "login_count": 15,
  "last_login": "2024-01-20T14:30:00Z"
}
```

## 安装和配置

### 1. 安装依赖
```bash
pip install bcrypt>=4.0.0
```

### 2. 数据库配置
确保 `config.py` 中包含正确的 MongoDB 配置：
```python
MONGODB_CONFIG = {
    "connection_string": "mongodb://localhost:27017",
    "db_name": "med_call_records"
}
```

## 使用方法

### 基本使用
```python
from tools.system_client import SystemClient, UserRole, SystemStatus

# 创建客户端实例
client = SystemClient()

# 初始化系统信息
await client.init_system_info()

# 创建用户
user_id = await client.create_user(
    username="admin",
    password="admin123",
    role=UserRole.ADMIN,
    full_name="系统管理员"
)

# 用户认证
user = await client.authenticate_user("admin", "admin123")

# 更新系统状态
await client.update_system_status(SystemStatus.RUNNING)

# 获取系统信息
sys_info = await client.get_system_info()

# 关闭连接
await client.close()
```

### 时间段配置管理
```python
# 获取当前时间段配置
time_slots = await client.get_call_time_slots()

# 更新时间段配置
new_slots = [
    {
        "name": "上午时段",
        "start_time": "09:00",
        "end_time": "12:00",
        "enabled": True
    },
    {
        "name": "下午时段",
        "start_time": "14:00",
        "end_time": "17:30",
        "enabled": True
    }
]
await client.update_call_time_slots(new_slots)

# 检查当前是否可以拨打电话
can_call = await client.is_in_call_time()
```

### 系统配置管理
```python
# 更新系统配置
config_updates = {
    "max_concurrent_calls": 5,
    "batch_size": 15,
    "skip_weekends": True
}
await client.update_system_config(config_updates)

# 获取特定配置
config = await client.get_system_config(["max_concurrent_calls", "batch_size"])
```

## 快速演示

运行完整功能演示：
```bash
cd tools
python system_client_example.py
```

运行基础功能测试：
```bash
cd tools
python system_client.py
```

## 权限说明

### 管理员权限 (UserRole.ADMIN)
- 系统配置管理
- 用户账号管理
- 时间段配置
- 系统维护操作
- 统计信息查看

### 医生权限 (UserRole.DOCTOR)
- 查看系统状态
- 查看统计信息
- 基本系统信息访问

## 安全特性

- **密码加密**: 使用 bcrypt 进行密码哈希存储
- **用户认证**: 支持用户名密码认证
- **账号管理**: 支持账号激活/停用
- **登录追踪**: 记录用户登录次数和最后登录时间

## 错误处理

系统提供完善的错误处理机制：
- 自动记录系统错误到数据库
- 支持错误记录查询和清理
- 异常状态监控和恢复

## 与现有系统集成

### 1. 迁移配置项
将 `config.py` 中的时间段配置迁移到数据库：
```python
# 原来在 config.py 中
SCHEDULE_CONFIG = {
    "time_slots": [...]
}

# 现在通过 SystemClient 管理
await client.update_call_time_slots(time_slots)
```

### 2. 动态配置读取
在主程序中使用动态配置：
```python
# 替代静态配置读取
client = SystemClient()
time_slots = await client.get_call_time_slots()
can_call = await client.is_in_call_time()
```

## 维护和监控

### 数据备份
```python
backup_data = await client.backup_system_data()
# 保存备份数据到文件
with open("system_backup.json", "w") as f:
    json.dump(backup_data, f, default=str)
```

### 系统健康检查
```python
stats = await client.get_system_statistics()
errors = await client.get_system_errors()

# 检查系统状态
if stats['system_status'] == '异常':
    # 处理异常情况
    pass
```

## 注意事项

1. **连接管理**: 使用完毕后记得调用 `await client.close()` 关闭数据库连接
2. **异常处理**: 所有异步操作都应包含在 try-catch 块中
3. **权限控制**: 根据用户权限限制操作访问
4. **数据验证**: 输入数据会进行格式验证
5. **并发安全**: 支持多实例并发访问

## 扩展建议

1. **Web界面**: 可以基于此模块开发Web管理界面
2. **API接口**: 可以封装为REST API提供给前端调用
3. **监控告警**: 集成监控系统，异常时自动告警
4. **定时任务**: 添加定时任务进行数据清理和健康检查
5. **审计日志**: 记录用户操作日志用于审计 