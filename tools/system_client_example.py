"""
系统客户端使用示例
展示如何使用 SystemClient 进行各种系统信息管理操作
"""

import asyncio
from system_client import SystemClient, UserRole, SystemStatus


async def main():
    """主函数，演示各种系统客户端功能"""
    
    # 创建系统客户端实例
    client = SystemClient()
    
    try:
        print("🚀 系统客户端功能演示")
        print("=" * 50)
        
        # 1. 初始化系统信息
        print("\n📋 1. 初始化系统信息...")
        success = await client.init_system_info()
        if success:
            print("✅ 系统信息初始化成功")
        else:
            print("❌ 系统信息初始化失败")
        
        # 2. 创建用户账号
        print("\n👤 2. 创建用户账号...")
        
        # 创建管理员
        try:
            admin_id = await client.create_user(
                username="admin",
                password="admin123",
                role=UserRole.ADMIN,
                full_name="系统管理员",
                email="<EMAIL>"
            )
            print(f"✅ 管理员创建成功，ID: {admin_id}")
        except ValueError as e:
            print(f"ℹ️  管理员可能已存在: {e}")
        
        # 创建医生
        try:
            doctor_id = await client.create_user(
                username="doctor01",
                password="doctor123",
                role=UserRole.DOCTOR,
                full_name="张医生",
                email="<EMAIL>",
                phone="13800138001"
            )
            print(f"✅ 医生创建成功，ID: {doctor_id}")
        except ValueError as e:
            print(f"ℹ️  医生可能已存在: {e}")
        
        # 3. 用户认证测试
        print("\n🔐 3. 用户认证测试...")
        user = await client.authenticate_user("admin", "admin123")
        if user:
            print(f"✅ 认证成功: {user['username']} ({user['role']})")
            print(f"   真实姓名: {user['full_name']}")
            print(f"   邮箱: {user.get('email', 'N/A')}")
            print(f"   登录次数: {user['login_count']}")
        else:
            print("❌ 认证失败")
        
        # 4. 获取所有用户
        print("\n👥 4. 获取用户列表...")
        users = await client.get_all_users()
        print(f"✅ 共有 {len(users)} 个用户:")
        for user in users:
            print(f"   - {user['username']} ({user['role']}) - {user['full_name']}")
        
        # 5. 系统状态管理
        print("\n🔄 5. 系统状态管理...")
        
        # 启动系统
        await client.update_system_status(SystemStatus.RUNNING)
        print("✅ 系统状态已更新为：运行中")
        
        # 获取系统信息
        sys_info = await client.get_system_info()
        if sys_info:
            print(f"   系统名称: {sys_info['system_name']}")
            print(f"   系统版本: {sys_info['version']}")
            print(f"   当前状态: {sys_info['status']}")
            print(f"   启动时间: {sys_info.get('start_time', 'N/A')}")
        
        # 6. 时间段配置管理
        print("\n⏰ 6. 电话拨打时间段配置...")
        time_slots = await client.get_call_time_slots()
        print("✅ 当前时间段配置:")
        for slot in time_slots:
            status = "启用" if slot['enabled'] else "禁用"
            print(f"   - {slot['name']}: {slot['start_time']}-{slot['end_time']} ({status})")
        
        # 检查当前是否可以拨打电话
        in_time = await client.is_in_call_time()
        print(f"✅ 当前是否可以拨打电话: {'是' if in_time else '否'}")
        
        # 7. 更新时间段配置（示例）
        print("\n⚙️  7. 更新时间段配置...")
        new_time_slots = [
            {
                "name": "上午时段",
                "start_time": "09:00",
                "end_time": "12:00",
                "enabled": True
            },
            {
                "name": "下午时段",
                "start_time": "14:00",
                "end_time": "17:30",
                "enabled": True
            },
            {
                "name": "晚上时段",
                "start_time": "19:00",
                "end_time": "21:00",
                "enabled": True  # 启用晚上时段
            }
        ]
        
        success = await client.update_call_time_slots(new_time_slots)
        if success:
            print("✅ 时间段配置更新成功")
        else:
            print("❌ 时间段配置更新失败")
        
        # 8. 系统配置管理
        print("\n⚙️  8. 系统配置管理...")
        
        # 更新系统配置
        config_updates = {
            "max_concurrent_calls": 5,
            "batch_size": 15,
            "skip_weekends": True
        }
        
        success = await client.update_system_config(config_updates)
        if success:
            print("✅ 系统配置更新成功")
        else:
            print("❌ 系统配置更新失败")
        
        # 获取特定配置
        specific_config = await client.get_system_config(["max_concurrent_calls", "batch_size"])
        print(f"✅ 当前并发配置: {specific_config}")
        
        # 9. 统计信息
        print("\n📊 9. 系统统计信息...")
        
        # 更新通话统计
        await client.update_call_statistics(calls_today=25, calls_total=150)
        
        # 获取统计信息
        stats = await client.get_system_statistics()
        print("✅ 系统统计:")
        print(f"   系统状态: {stats.get('system_status')}")
        print(f"   运行时长: {stats.get('current_running_duration', 0)} 秒")
        print(f"   今日通话数: {stats.get('total_calls_today', 0)}")
        print(f"   总通话数: {stats.get('total_calls_all', 0)}")
        print(f"   错误次数: {stats.get('error_count', 0)}")
        
        # 10. 错误处理演示
        print("\n🔥 10. 错误处理演示...")
        
        # 模拟系统错误
        await client.update_system_status(SystemStatus.ERROR, "测试错误信息")
        print("✅ 已记录测试错误")
        
        # 获取错误记录
        errors = await client.get_system_errors(limit=5)
        print(f"✅ 系统错误记录 (最近5条):")
        for error in errors:
            print(f"   - {error['timestamp']}: {error['message']}")
        
        # 11. 系统维护
        print("\n🔧 11. 系统维护...")
        
        # 数据备份
        backup_data = await client.backup_system_data()
        if backup_data:
            print(f"✅ 数据备份成功，备份时间: {backup_data['backup_time']}")
            print(f"   备份包含 {len(backup_data['data'])} 个文档")
        
        # 清空错误记录
        success = await client.clear_system_errors()
        if success:
            print("✅ 系统错误记录已清空")
        
        # 12. 密码管理
        print("\n🔑 12. 密码管理...")
        
        # 更新密码
        success = await client.update_user_password("doctor01", "new_password123")
        if success:
            print("✅ 医生密码更新成功")
        
        # 用新密码测试登录
        user = await client.authenticate_user("doctor01", "new_password123")
        if user:
            print("✅ 新密码登录成功")
        else:
            print("❌ 新密码登录失败")
        
        # 13. 最终系统状态
        print("\n🏁 13. 最终系统状态...")
        
        # 停止系统
        await client.update_system_status(SystemStatus.STOPPED)
        print("✅ 系统已停止")
        
        # 获取最终统计
        final_stats = await client.get_system_statistics()
        print(f"✅ 最终运行时长: {final_stats.get('running_duration', 0)} 秒")
        
        print("\n🎉 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 关闭数据库连接
        await client.close()
        print("\n🔌 数据库连接已关闭")


if __name__ == "__main__":
    """运行演示"""
    print("🚀 启动系统客户端演示...")
    print("⚠️  请确保MongoDB服务已启动")
    
    # 等待用户确认
    input("\n按回车键开始演示...")
    
    # 运行演示
    asyncio.run(main()) 