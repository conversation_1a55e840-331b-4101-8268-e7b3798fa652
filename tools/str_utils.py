import re
import json
from typing import Optional, Dict, Any


async def extract_json_regex(response: str, xml_tag: str = "output") -> Optional[Dict[str, Any]]:
    """使用正则表达式提取JSON"""
    pattern = f"<{xml_tag}>(.*?)</{xml_tag}>"
    match = re.search(pattern, response, re.DOTALL)

    if match:
        json_str = match.group(1).strip()
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            return None
    return None


async def extract_json_string_ops(response: str, xml_tag: str = "output") -> Optional[Dict[str, Any]]:
    """使用字符串操作提取JSON"""
    start_tag = f"<{xml_tag}>"
    end_tag = f"</{xml_tag}>"

    start_index = response.find(start_tag)
    end_index = response.find(end_tag)

    if start_index != -1 and end_index != -1:
        start_index += len(start_tag)
        json_str = response[start_index:end_index].strip()
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            return None
    return None


async def extract_json_fallback(response: str) -> Optional[Dict[str, Any]]:
    """备用提取方法：查找大括号包围的内容"""
    # 查找第一个 { 和最后一个 }
    start = response.find('{')
    end = response.rfind('}')

    if start != -1 and end != -1 and end > start:
        json_str = response[start:end + 1]
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            return None
    return None

async def extract_json_fallback2(response: str) -> str:
    """备用提取方法：查找大括号包围的内容"""
    # 查找第一个 { 和最后一个 }
    start = response.find('{')
    end = response.rfind('}')

    if start != -1 and end != -1 and end > start:
        json_str = response[start:end + 1]
        return json_str
    return response

async def smart_extract_json(response: str, xml_tag: str = "output") -> Optional[Dict[str, Any]]:
    """智能提取JSON：尝试多种方法"""
    # 方法1：正则表达式提取XML标签
    result = await extract_json_regex(response, xml_tag)
    if result:
        return result

    # 方法2：字符串操作提取XML标签
    result = extract_json_string_ops(response, xml_tag)
    if result:
        return await result

    # 方法3：备用方法，直接查找JSON结构
    result = extract_json_fallback(response)
    if result:
        return await result

    return None

async def ends_comma(acc_chunk):
    if (acc_chunk.endswith("，") or acc_chunk.endswith("。") or acc_chunk.endswith("？")
            or acc_chunk.endswith(",") or acc_chunk.endswith(".") or acc_chunk.endswith("?")
            or acc_chunk.endswith("！") or acc_chunk.endswith("!")):
        return True
    return False

import random
async def choice_interval_speak():
    s = ['请稍等', '请稍等', '请稍等', '我想一下', '收到了', '收到了','收到了', '我看一下', '正在处理', '我思考一下', '处理中']
    return random.choice(s)

async def split_chinese_text(text):
    # 使用中文句号、问号、感叹号进行分割
    pattern = r'[。？！]'
    result = re.split(pattern, text)
    # 过滤掉空字符串
    return [item.strip() for item in result if item.strip()]

async def remove_last_comma(user_text):
    user_text = str(user_text).replace('。', '').replace('？', '').replace('！', '')
    return user_text