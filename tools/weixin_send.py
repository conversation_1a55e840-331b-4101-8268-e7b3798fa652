import aiohttp
import asyncio
import json
from typing import Dict, Any, Optional
from datetime import datetime

webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f4ba3c6c-d8f7-422e-a880-eeebf4bdf3ad"

class WeChatWebhookSender:
    """企业微信Webhook异步发送器"""

    def __init__(self, webhook_url: str):
        """
        初始化Webhook发送器

        Args:
            webhook_url: 企业微信Webhook地址
        """
        self.webhook_url = webhook_url
        self.headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }

    async def send_patient_report(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送患者训练情况报告

        Args:
            patient_data: 患者数据字典

        Returns:
            响应结果字典，包含status_code和response_data
        """
        try:
            markdown_content = self._build_markdown_content(patient_data)

            # 构建请求数据
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": markdown_content
                }
            }

            return await self._send_message(data)

        except Exception as e:
            return {
                "success": False,
                "error": f"构建消息失败: {str(e)}",
                "status_code": None,
                "response_data": None
            }

    async def send_custom_markdown(self, markdown_content: str) -> Dict[str, Any]:
        """
        发送自定义Markdown消息

        Args:
            markdown_content: Markdown格式的消息内容

        Returns:
            响应结果字典
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": markdown_content
            }
        }

        return await self._send_message(data)

    async def send_text_message(self, content: str, mentioned_list: Optional[list] = None) -> Dict[str, Any]:
        """
        发送文本消息

        Args:
            content: 文本内容
            mentioned_list: @用户列表（可选）

        Returns:
            响应结果字典
        """
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }

        if mentioned_list:
            data["text"]["mentioned_list"] = mentioned_list

        return await self._send_message(data)

    async def _send_message(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送消息的内部方法

        Args:
            data: 要发送的数据

        Returns:
            响应结果字典
        """
        try:
            # 确保JSON序列化使用UTF-8编码
            json_data = json.dumps(data, ensure_ascii=False, indent=None, separators=(',', ':'))

            async with aiohttp.ClientSession() as session:
                async with session.post(
                        self.webhook_url,
                        headers=self.headers,
                        data=json_data.encode('utf-8'),
                        timeout=aiohttp.ClientTimeout(total=30)
                ) as response:

                    response_text = await response.text()

                    try:
                        response_data = json.loads(response_text)
                    except json.JSONDecodeError:
                        response_data = {"raw_response": response_text}

                    result = {
                        "success": response.status == 200 and response_data.get("errcode", 0) == 0,
                        "status_code": response.status,
                        "response_data": response_data,
                        "error": None
                    }

                    # 如果有错误码，添加错误信息
                    if response_data.get("errcode", 0) != 0:
                        result["error"] = f"API错误: {response_data.get('errmsg', '未知错误')}"

                    return result

        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": "请求超时",
                "status_code": None,
                "response_data": None
            }
        except aiohttp.ClientError as e:
            return {
                "success": False,
                "error": f"网络请求失败: {str(e)}",
                "status_code": None,
                "response_data": None
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"发送消息失败: {str(e)}",
                "status_code": None,
                "response_data": None
            }

    def _build_markdown_content(self, patient_data: Dict[str, Any]) -> str:
        """
        构建Markdown格式的患者报告内容

        Args:
            patient_data: 患者数据字典

        Returns:
            Markdown格式的字符串
        """
        # 获取训练完成状态的颜色
        training_status = patient_data.get('训练完成情况', 'N/A')
        training_color = "green" if training_status == "完成" else "warning"

        # 获取不适感状态的颜色
        discomfort_status = patient_data.get('是否有不适感', 'N/A')
        discomfort_color = "green" if discomfort_status == "否" else "warning"

        # 获取设备状态的颜色
        device_status = patient_data.get('锻炼辅助仪器是否有问题', 'N/A')
        device_color = "green" if device_status == "否" else "warning"

        # 获取是否需要医生联系的颜色
        doctor_contact = patient_data.get('是否需要医生人工和患者联系', 'N/A')
        doctor_color = "green" if doctor_contact == "否" else "warning"

        return f"""# 患者训练情况报告 📋

## 👤 患者信息
**患者姓名**：<font color="info">{patient_data.get('患者名字', 'N/A')}</font>
**联系电话**：<font color="info">{patient_data.get('手机号', 'N/A')}</font>
**通话时间**：<font color="comment">{patient_data.get('通话时间', 'N/A')}</font>
**拨号状态**：<font color="comment">{patient_data.get('拨号状态', 'N/A')}</font>

## 🏃 训练情况
> **训练完成情况**：<font color="{training_color}">{training_status}</font> {'✅' if training_status == '完成' else '⚠️'}
> **训练次数**：<font color="warning">{patient_data.get('训练次数', 'N/A')}</font> 
> **训练时长**：<font color="warning">{patient_data.get('训练时长', 'N/A')}</font>

## 🏥 健康状况
**是否有不适感**：<font color="{discomfort_color}">{discomfort_status}</font> {'✅' if discomfort_status == '否' else '⚠️'}
**不适感内容**：{patient_data.get('不适感内容', 'N/A')}
**锻炼结束后不适感是否恢复**：<font color="{'green' if patient_data.get('不适感锻炼结束后是否恢复') == '是' else 'warning'}">{patient_data.get('不适感锻炼结束后是否恢复', 'N/A')}</font>

## 🔧 设备状况
**锻炼辅助仪器是否有问题**：<font color="{device_color}">{device_status}</font> {'✅' if device_status == '否' else '⚠️'}
**设备问题详情**：{patient_data.get('锻炼辅助仪器问题内容', 'N/A')}

## 👨‍⚕️ 医生介入
**是否需要医生人工联系**：<font color="{doctor_color}">{doctor_contact}</font> {'✅' if doctor_contact == '否' else '🚨'}

---
<font color="comment">报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</font>"""

    def _get_alert_level(self, patient_data: Dict[str, Any]) -> str:
        """
        根据患者数据判断预警级别

        Args:
            patient_data: 患者数据字典

        Returns:
            预警级别字符串
        """
        alerts = []

        # 检查训练完成情况
        if patient_data.get('训练完成情况') != '完成':
            alerts.append('训练未完成')

        # 检查不适感
        if patient_data.get('是否有不适感') == '是':
            alerts.append('存在不适感')

        # 检查不适感恢复情况
        if patient_data.get('不适感锻炼结束后是否恢复') == '否':
            alerts.append('不适感未恢复')

        # 检查设备问题
        if patient_data.get('锻炼辅助仪器是否有问题') == '是':
            alerts.append('设备异常')

        # 检查是否需要医生介入
        if patient_data.get('是否需要医生人工和患者联系') == '是':
            alerts.append('需要医生介入')

        if not alerts:
            return "正常"
        elif len(alerts) == 1:
            return "轻微异常"
        elif len(alerts) <= 3:
            return "中度异常"
        else:
            return "严重异常"

    async def send_patient_alert(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送患者预警消息（当有异常情况时）

        Args:
            patient_data: 患者数据字典

        Returns:
            响应结果字典
        """
        alert_level = self._get_alert_level(patient_data)

        if alert_level == "正常":
            # 正常情况发送常规报告
            return await self.send_patient_report(patient_data)

        # 异常情况发送预警消息
        alert_content = f"""🚨 患者异常情况预警

**患者姓名**：{patient_data.get('患者名字', 'N/A')}
**预警级别**：<font color="warning">{alert_level}</font>
**通话时间**：{patient_data.get('通话时间', 'N/A')}

**异常详情**：
"""

        # 添加具体异常信息
        if patient_data.get('训练完成情况') != '完成':
            alert_content += f"- ❌ 训练未完成：{patient_data.get('训练完成情况', 'N/A')}\n"

        if patient_data.get('是否有不适感') == '是':
            alert_content += f"- ⚠️ 存在不适感：{patient_data.get('不适感内容', 'N/A')}\n"

        if patient_data.get('不适感锻炼结束后是否恢复') == '否':
            alert_content += "- 🔴 不适感未恢复\n"

        if patient_data.get('锻炼辅助仪器是否有问题') == '是':
            alert_content += f"- 🔧 设备异常：{patient_data.get('锻炼辅助仪器问题内容', 'N/A')}\n"

        if patient_data.get('是否需要医生人工和患者联系') == '是':
            alert_content += "- 👨‍⚕️ **需要医生立即联系患者**\n"

        alert_content += f"\n请及时处理！\n联系电话：{patient_data.get('手机号', 'N/A')}"

        return await self.send_custom_markdown(alert_content)


# 便捷函数
async def send_patient_report_async(patient_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷函数：发送患者报告

    Args:
        webhook_url: Webhook地址
        patient_data: 患者数据

    Returns:
        发送结果
    """
    sender = WeChatWebhookSender(webhook_url)
    return await sender.send_patient_report(patient_data)


async def send_patient_alert_async(patient_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷函数：发送患者预警（智能判断是否需要预警）

    Args:
        webhook_url: Webhook地址
        patient_data: 患者数据

    Returns:
        发送结果
    """
    sender = WeChatWebhookSender(webhook_url)
    return await sender.send_patient_alert(patient_data)


# 使用示例
async def main():
    """使用示例"""

    # Webhook地址


    # 实际的患者数据格式
    patient_data = {
        "患者名字": "李四",
        "手机号": "17764016802",
        "通话时间": "2025-06-08 12:34",
        "拨号状态": "通话结束",
        "训练完成情况": "完成",
        "训练次数": "100次",
        "训练时长": "15分钟",
        "是否有不适感": "否",
        "不适感内容": "腰疼",
        "不适感锻炼结束后是否恢复": "否",
        "是否需要医生人工和患者联系": "否",
        "锻炼辅助仪器是否有问题": "否",
        "锻炼辅助仪器问题内容": "正常"
    }

    # 创建发送器实例
    sender = WeChatWebhookSender(webhook_url)

    # 发送智能预警报告（会根据情况自动判断发送普通报告还是预警消息）
    result = await sender.send_patient_report(patient_data)

    print(f"发送结果: {result}")

    if result["success"]:
        print("✅ 消息发送成功")
    else:
        print(f"❌ 消息发送失败: {result['error']}")
        if result["response_data"]:
            print(f"响应数据: {result['response_data']}")


# 异常情况测试示例
async def demo_alert():
    """测试预警功能"""

    webhook_url = "your_webhook_url_here"

    # 模拟异常情况的患者数据
    alert_patient_data = {
        "患者名字": "张三",
        "手机号": "13800138000",
        "通话时间": "2025-06-08 14:20",
        "拨号状态": "通话结束",
        "训练完成情况": "未完成",  # 异常
        "训练次数": "50次",
        "训练时长": "8分钟",
        "是否有不适感": "是",  # 异常
        "不适感内容": "膝盖疼痛",
        "不适感锻炼结束后是否恢复": "否",  # 异常
        "是否需要医生人工和患者联系": "是",  # 需要医生介入
        "锻炼辅助仪器是否有问题": "否",
        "锻炼辅助仪器问题内容": "正常"
    }

    # 发送预警消息
    result = await send_patient_alert_async(webhook_url, alert_patient_data)
    return result


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())