from pprint import pprint

import requests
import json
import os
from typing import Optional, List, Dict, Any
from urllib.parse import urljoin


class XSwitchMediaClient:
    """XSwitch媒体文件管理客户端"""

    def __init__(self, base_url: str, auth_id: str = None, username: str = None, password: str = None):
        """
        初始化客户端

        Args:
            base_url: XSwitch服务器地址，如 "https://tycy.xswitch.cn"
            auth_id: 认证ID（可选，如果提供则直接使用）
            username: 用户名（用于自动登录获取auth_id）
            password: 密码（用于自动登录获取auth_id）
        """
        self.base_url = base_url.rstrip('/')
        self.auth_id = auth_id
        self.username = username
        self.password = password
        self.session_info = None

        # 如果没有提供auth_id但提供了用户名密码，则自动登录
        if not self.auth_id and self.username and self.password:
            self.auth_id = self.login()

        if not self.auth_id:
            raise ValueError("必须提供auth_id或者用户名密码进行认证")

        self.headers = {
            'X-XTRA-AUTH-ID': self.auth_id,
            'Content-Type': 'application/json'
        }

    def login(self) -> Optional[str]:
        """
        使用用户名密码登录获取Token

        Returns:
            Token字符串，失败返回None
        """
        if not self.username or not self.password:
            print("缺少用户名或密码")
            return None

        url = f"{self.base_url}/api/sessions"

        # 支持两种数据格式
        # 方式1: application/x-www-form-urlencoded
        data = f"login={self.username}&password={self.password}"
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        try:
            print(f"正在登录到 {self.base_url}...")
            response = requests.post(url, data=data, headers=headers)
            response.raise_for_status()

            result = response.json()
            if result.get('code') == 200:
                token = result.get('token')
                self.session_info = result
                print(f"登录成功，用户ID: {result.get('user_id')}, 分机号: {result.get('extn')}")
                print(f"Token过期时间: {result.get('expires')}")
                return token
            else:
                print(f"登录失败: {result}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"登录请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"解析登录响应失败: {e}")
            return None

    def login_with_json(self) -> Optional[str]:
        """
        使用JSON格式登录获取Token

        Returns:
            Token字符串，失败返回None
        """
        if not self.username or not self.password:
            print("缺少用户名或密码")
            return None

        url = f"{self.base_url}/api/sessions"

        # 方式2: application/json
        data = {
            "login": self.username,
            "password": self.password
        }
        headers = {
            'Content-Type': 'application/json'
        }

        try:
            print(f"正在登录到 {self.base_url} (JSON格式)...")
            response = requests.post(url, json=data, headers=headers)
            response.raise_for_status()

            result = response.json()
            if result.get('code') == 200:
                token = result.get('token')
                self.session_info = result
                print(f"登录成功，用户ID: {result.get('user_id')}, 分机号: {result.get('extn')}")
                return token
            else:
                print(f"登录失败: {result}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"登录请求失败: {e}")
            return None

    def logout(self) -> bool:
        """
        注销当前会话

        Returns:
            注销是否成功
        """
        if not self.auth_id:
            print("没有有效的认证Token")
            return False

        url = f"{self.base_url}/api/sessions"

        try:
            response = requests.delete(url, headers=self.headers)
            response.raise_for_status()

            result = response.json()
            if result.get('code') == 200:
                print("注销成功")
                self.auth_id = None
                self.session_info = None
                return True
            else:
                print(f"注销失败: {result}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"注销请求失败: {e}")
            return False

    def refresh_token_if_needed(self):
        """
        检查并刷新Token（如果需要的话）
        """
        if not self.auth_id and self.username and self.password:
            print("Token已过期，正在重新登录...")
            new_token = self.login()
            if new_token:
                self.auth_id = new_token
                self.headers['X-XTRA-AUTH-ID'] = new_token
                print("Token刷新成功")
            else:
                print("Token刷新失败")

    def _make_request(self, method: str, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        统一的请求方法，包含Token过期重试机制

        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数

        Returns:
            响应数据或None
        """
        try:
            response = requests.request(method, url, headers=self.headers, **kwargs)

            # 如果返回403可能是Token过期，尝试重新登录
            if response.status_code == 403 and self.username and self.password:
                print("检测到认证失败，正在尝试重新登录...")
                self.refresh_token_if_needed()
                # 重试请求
                response = requests.request(method, url, headers=self.headers, **kwargs)

            response.raise_for_status()

            # 如果是下载文件请求，直接返回response对象
            if 'stream' in kwargs and kwargs['stream']:
                return response

            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None

    def get_all_media_files(self, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """
        获取所有媒体文件信息

        Args:
            page: 页码
            per_page: 每页数量

        Returns:
            媒体文件列表信息
        """
        url = f"{self.base_url}/api/media_files"
        params = {'page': page, 'perPage': per_page}

        result = self._make_request('GET', url, params=params)
        return result if result else {}

    def get_media_files_by_type(self, file_type: str, page: int = 1) -> Dict[str, Any]:
        """
        根据类型获取媒体文件

        Args:
            file_type: 文件类型 (SYSTEM, UPLOAD, RECORD, SCRIPT, DATABASE, ASR, VOICEMAIL, TTS, PDF, FAX)
            page: 页码

        Returns:
            筛选后的媒体文件信息
        """
        url = f"{self.base_url}/api/media_files"
        params = {'type': file_type, 'page': page}

        result = self._make_request('GET', url, params=params)
        if not result:
            print(f"获取{file_type}类型媒体文件失败")
            return {}
        return result

    def get_media_files_by_uuid(self, uuid: str) -> Dict[str, Any]:
        """
        根据通话UUID获取相关媒体文件

        Args:
            uuid: 通话UUID

        Returns:
            相关媒体文件信息
        """
        url = f"{self.base_url}/api/media_files"
        params = {'uuid': uuid}

        result = self._make_request('GET', url, params=params)
        if not result:
            print(f"获取UUID {uuid} 相关媒体文件失败")
            return {}
        return result

    def get_media_file_info(self, file_id: int) -> Dict[str, Any]:
        """
        获取指定ID的媒体文件信息

        Args:
            file_id: 媒体文件ID

        Returns:
            媒体文件详细信息
        """
        url = f"{self.base_url}/api/media_files/{file_id}"

        result = self._make_request('GET', url)
        if not result:
            print(f"获取媒体文件 {file_id} 信息失败")
            return {}
        return result

    def get_media_file_info_by_uuid(self, uuid: str) -> Dict[str, Any]:
        """
        根据UUID获取媒体文件信息

        Args:
            uuid: 媒体文件UUID

        Returns:
            媒体文件信息
        """
        url = f"{self.base_url}/api/media_files"
        params = {'uuid': uuid}

        result = self._make_request('GET', url, params=params)
        if not result:
            print(f"获取UUID {uuid} 媒体文件信息失败")
            return {}
        return result

    def download_media_file(self, file_id: int, file_ext: str, save_path: str = None) -> bool:
        """
        下载媒体文件

        Args:
            file_id: 媒体文件ID
            file_ext: 文件扩展名 (如 'wav', 'mp3')
            save_path: 保存路径，如果不指定则保存到当前目录

        Returns:
            下载是否成功
        """
        url = f"{self.base_url}/api/media_files/{file_id}.{file_ext}"

        try:
            # 首先获取文件信息以获得文件名
            file_info = self.get_media_file_info(file_id)
            if not file_info:
                print(f"无法获取文件 {file_id} 的信息")
                return False

            # 确定保存的文件名
            if save_path is None:
                filename = file_info.get('name', f'media_file_{file_id}')
                if not filename.endswith(f'.{file_ext}'):
                    filename = f"{filename}.{file_ext}"
                save_path = filename

            # 下载文件
            response = self._make_request('GET', url, stream=True)
            if not response:
                return False

            # 保存文件
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            print(f"文件下载成功: {save_path}")
            return True

        except IOError as e:
            print(f"保存文件失败: {e}")
            return False

    def get_recording_files(self) -> List[Dict[str, Any]]:
        """
        获取所有录音文件

        Returns:
            录音文件列表
        """
        result = self.get_media_files_by_type('RECORD')
        return result.get('data', [])

    def download_all_recordings(self, save_dir: str = 'recordings') -> None:
        """
        下载所有录音文件

        Args:
            save_dir: 保存目录
        """
        # 创建保存目录
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        recordings = self.get_recording_files()

        if not recordings:
            print("没有找到录音文件")
            return

        print(f"找到 {len(recordings)} 个录音文件，开始下载...")

        for recording in recordings:
            file_id = recording.get('id')
            file_ext = recording.get('ext', 'wav')
            file_name = recording.get('name', f'recording_{file_id}')

            # 确保文件名包含扩展名
            if not file_name.endswith(f'.{file_ext}'):
                file_name = f"{file_name}.{file_ext}"

            save_path = os.path.join(save_dir, file_name)

            print(f"正在下载: {file_name}")
            self.download_media_file(file_id, file_ext, save_path)


def main():
    """示例用法"""
    # 配置XSwitch服务器信息
    BASE_URL = "https://tycy.xswitch.cn"

    # 方式1: 使用用户名密码自动登录
    USERNAME = "admin"  # 替换为你的用户名
    PASSWORD = "Vy8@EoNWG}"  # 替换为你的密码

    # 方式2: 直接使用已知的AUTH_ID
    # AUTH_ID = "0115649b-47d4-49e7-8438-69937cae0b4c"
    channel_id = "0115649b-47d4-49e7-8438-69937cae0b4c"  # 替换为你的认证ID
    # channel_id = "737ced13-0c84-47fb-ae9a-cb6baeae1148"  # 替换为你的认证ID

    try:
        # 创建客户端 - 使用用户名密码自动登录
        client = XSwitchMediaClient(
            base_url=BASE_URL,
            username=USERNAME,
            password=PASSWORD
        )
        res = client.get_all_media_files()
        pprint(res)

        # 或者使用已知的AUTH_ID
        # client = XSwitchMediaClient(base_url=BASE_URL, auth_id=AUTH_ID)

        # 示例1: 获取所有媒体文件
        # print("=== 获取所有媒体文件 ===")
        # all_files = client.get_all_media_files(page=1, per_page=10)
        # if all_files:
        #     print(f"总共 {all_files.get('rowCount', 0)} 个文件")
        #     for file_info in all_files.get('data', []):
        #         print(f"ID: {file_info.get('id')}, 名称: {file_info.get('name')}, "
        #               f"类型: {file_info.get('type')}, 大小: {file_info.get('file_size')} 字节")
        #
        # # 示例2: 获取录音文件
        # print("\n=== 获取录音文件 ===")
        # recordings = client.get_recording_files()
        # if recordings:
        #     print(f"找到 {len(recordings)} 个录音文件")
        #     for recording in recordings:
        #         print(f"录音ID: {recording.get('id')}, 名称: {recording.get('name')}, "
        #               f"通话UUID: {recording.get('channel_uuid')}")
        #
        # # 示例3: 下载特定录音文件
        # if recordings:
        #     first_recording = recordings[0]
        #     file_id = first_recording.get('id')
        #     file_ext = first_recording.get('ext', 'wav')
        #
        #     print(f"\n=== 下载录音文件 ID: {file_id} ===")
        #     success = client.download_media_file(file_id, file_ext)
        #     if success:
        #         print("录音文件下载成功")
        #     else:
        #         print("录音文件下载失败")
        #
        # # 示例4: 批量下载所有录音文件
        # print("\n=== 批量下载所有录音文件 ===")
        # client.download_all_recordings('downloaded_recordings')

        # 注销会话（可选）
        client.logout()

    except ValueError as e:
        print(f"初始化客户端失败: {e}")
    except Exception as e:
        print(f"程序执行出错: {e}")


if __name__ == "__main__":
    main()