"""
RPC工具类：封装所有NATS RPC请求的组装和发送
"""

import json
import uuid
import asyncio
from nats.errors import TimeoutError
from med_config import TTS_CONFIG, ASR_CONFIG
from log_utils import debug, info, warning, error


class RPCUtils:
    def __init__(self, nats_client, service, ctrl_uuid):
        self.nats_client = nats_client
        self.service = service
        self.ctrl_uuid = ctrl_uuid

    def _default_rpc(self):
        """创建默认的RPC请求模板"""
        return {
            "jsonrpc": "2.0",
            "id": "fake-uuid-answer",
            "method": "XNode.Answer",
            "params": {
                "ctrl_uuid": self.ctrl_uuid
            }
        }

    async def _send_request(self, rpc, timeout=100):
        """发送RPC请求的通用方法"""
        try:
            rpc_json = json.dumps(rpc)
            debug("发送RPC请求: %s", rpc_json)
            
            response = await self.nats_client.request(
                self.service,
                bytes(rpc_json, "utf-8"),
                # rpc_json,
                timeout=timeout
            )
            
            result = json.loads(response.data.decode())
            debug("收到RPC响应: %s", result)
            return result
            
        except TimeoutError:
            warning("RPC请求超时: %s", rpc.get('method', 'Unknown'))
            return {"result": {"code": 408, "message": "Request Timeout"}}
        except Exception as e:
            error("RPC请求出错: %s", e)
            return {"result": {"code": 500, "message": str(e)}}

    async def dial(self, dial_string, call_uuid=None, timeout=20):
        """拨打电话"""
        if call_uuid is None:
            call_uuid = str(uuid.uuid4())

        rpc = self._default_rpc()
        rpc['method'] = "XNode.Dial"
        rpc['id'] = "call1-"+call_uuid
        rpc['params'].update({
            "destination": {
                "global_params": {
                    "ignore_early_media": True
                },
                "call_params": [{
                    "uuid": call_uuid,
                    "dial_string": dial_string,
                    "params": {
                        "leg_timeout": str(timeout),
                        "absolute_codec_string": "PCMA,PCMU"
                    }
                }]
            }
        })

        response = await self._send_request(rpc, timeout + 5)
        return call_uuid, response

    async def answer(self, channel_uuid):
        """应答电话"""
        rpc = self._default_rpc()
        rpc["id"] = "fake-uuid-answer"
        rpc["method"] = "XNode.Answer"
        rpc["params"]["uuid"] = channel_uuid

        return await self._send_request(rpc)

    async def accept(self, channel_uuid):
        """接管来电呼叫"""
        rpc = self._default_rpc()
        rpc["id"] = "fake-uuid-accept"
        rpc["method"] = "XNode.Accept"
        rpc["params"]["uuid"] = channel_uuid

        return await self._send_request(rpc)

    async def start_continuous_asr(self, channel_uuid):
        """启动连续语音识别（不传media参数）"""
        rpc = self._default_rpc()
        rpc["id"] = f"continuous-asr-{str(uuid.uuid4())}"
        rpc["method"] = "XNode.DetectSpeech"
        rpc["params"].update({
            "uuid": channel_uuid,
            "speech": ASR_CONFIG
        })

        return await self._send_request(rpc)

    async def play_tts(self, channel_uuid, text):
        """播放TTS"""
        rpc = self._default_rpc()
        rpc["id"] = f"tts-{str(uuid.uuid4())}"
        rpc["method"] = "XNode.Play"
        rpc["params"].update({
            "uuid": channel_uuid,
            "media": {
                "data": text,
                "type": "TEXT",
                "engine": 'xunfei',
                "voice": "x4_yezi"
            }
        })

        return await self._send_request(rpc)

    async def xnode_detect(self,  channel_uuid, text):
        """
        这个请求首先播放tts语音，然后接受用户输入并识别ASR，支持打断
        发送语音识别请求
        params:
            service: 服务名称
            channel_uuid: 通道UUID
            text: 要播放的文本
        """
        rpc = self._default_rpc()
        rpc["id"] = 'my-detect'
        rpc["method"] = 'XNode.DetectSpeech'
        rpc["params"].update({
            "uuid": channel_uuid,
            # 语音识别配置
            "speech": {
				"engine": 'xunfei',
				"grammar": "default",
				"no_input_timeout": 25000,# 无输入超时时间（5秒）
				"speech_timeout": 18000,# 语音超时时间（8秒）
				"partial":True,
				"partial_events": True,# 是否启用部分事件
				"disable_detected_data_event": False,# 是否禁用检测到的数据事件
				"nobreak": False,# 是否禁用断句
				"break_delay": 2000,
				"merge_delimiter":"。",
				"params": {
					# "asr-result-fire-switch-event": True,
					# "asr-result-fire-session-event" : True,
					"silence-ms": "700",# 静音时间（1秒）
					"add-punc": "true",
                    "vad-mode": "-1",
                    "vad-debug": "1",
					"segment-recording-prefix": "/usr/local/freeswitch/storage/recordings/segment-recording-2024-"# 录音文件前缀
				}
			},
			# TTS配置
			"media": {
				"data": text,#text,
				"type": "TEXT",
				"engine": 'xunfei',
				"voice": "x4_yezi"
			}
        })

        return await self._send_request(rpc)

    async def stop_detect_speech(self, channel_uuid):
        """停止语音识别"""
        rpc = self._default_rpc()
        rpc["id"] = f"stop-asr-{str(uuid.uuid4())}"
        rpc["method"] = "XNode.StopDetectSpeech"
        rpc["params"]["uuid"] = channel_uuid

        return await self._send_request(rpc)

    async def hangup(self, channel_uuid):
        """挂断电话"""
        rpc = self._default_rpc()
        rpc["id"] = "fake-uuid-hangup"
        rpc["method"] = "XNode.Hangup"
        rpc["params"]["uuid"] = channel_uuid

        return await self._send_request(rpc) 