#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试邮件内容中的入组日期
"""

import asyncio
from datetime import datetime
from tools.patient_email_sender import send_patient_report_email_async, PatientEmailSender
from log_utils import info


async def test_enrollment_date_in_email():
    """测试邮件内容中的入组日期"""
    info("=== 测试邮件内容中的入组日期 ===")
    
    # 模拟通话记录数据（使用真实的患者手机号）
    patient_data = {
        "患者名字": "张三",
        # 不包含患者编号，让系统自动查询
        "手机号": "18971492577",  # 使用数据库中真实存在的手机号
        "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "记录日期": datetime.now().strftime("%Y-%m-%d"),
        "拨号状态": "通话结束",
        "训练完成情况": "未完成",
        "训练次数": "50次",
        "训练时长": "8分钟",
        "是否有不适感": "是",
        "不适感内容": "膝盖疼痛严重",
        "不适感锻炼结束后是否恢复": "否",
        "是否需要医生人工和患者联系": "是",
        "锻炼辅助仪器是否有问题": "否",
        "锻炼辅助仪器问题内容": "正常"
    }
    
    try:
        info("正在发送包含入组日期的邮件...")
        result = await send_patient_report_email_async(patient_data)
        
        if result["success"]:
            info("✅ 邮件发送成功!")
            details = result.get("details", {})
            info(f"  邮件主题: {details.get('subject')}")
            info(f"  患者编号: {details.get('patient_id')}")
            info(f"  医生邮箱: {details.get('doctor_email')}")
            
        else:
            info(f"❌ 邮件发送失败: {result['error']}")
            if result.get("details"):
                info(f"详细信息: {result['details']}")
                
    except Exception as e:
        info(f"测试过程中出错: {e}")


async def test_email_content_generation():
    """测试邮件内容生成（不实际发送）"""
    info("=== 测试邮件内容生成 ===")
    
    patient_data = {
        "患者名字": "张三",
        "手机号": "18971492577",
        "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "是否有不适感": "是",
        "不适感内容": "膝盖疼痛",
        "是否需要医生人工和患者联系": "是"
    }
    
    try:
        sender = PatientEmailSender()
        
        # 获取患者信息
        patient_info = await sender.doctor_manager.get_patient_info_from_call_record(patient_data)
        if patient_info:
            info("查询到的患者信息:")
            info(f"  患者编号: {patient_info.get('patient_id')}")
            info(f"  入组日期: {patient_info.get('enrollment_date')}")
            info(f"  年龄: {patient_info.get('age')}")
            info(f"  性别: {patient_info.get('gender')}")
            info(f"  训练状态: {patient_info.get('training_status')}")
            info(f"  分配医生: {patient_info.get('assigned_doctor_name')}")
            
            # 创建增强的患者数据
            enhanced_patient_data = patient_data.copy()
            enhanced_patient_data['患者编号'] = patient_info.get('patient_id', 'N/A')
            enhanced_patient_data['入组日期'] = patient_info.get('enrollment_date', 'N/A')
            enhanced_patient_data['患者年龄'] = patient_info.get('age', 'N/A')
            enhanced_patient_data['患者性别'] = patient_info.get('gender', 'N/A')
            enhanced_patient_data['训练状态'] = patient_info.get('training_status', 'N/A')
            enhanced_patient_data['分配医生'] = patient_info.get('assigned_doctor_name', 
                                                        patient_info.get('assigned_doctor', 'N/A'))
            
            # 生成邮件内容
            email_content = sender._build_email_content(enhanced_patient_data)
            
            info("生成的邮件内容:")
            print("\n" + "="*60)
            print(email_content)
            print("="*60 + "\n")
            
            # 检查关键信息
            if "入组日期：2024-01-15" in email_content:
                info("✅ 邮件内容中包含正确的入组日期")
            elif "入组日期：N/A" in email_content:
                info("⚠️ 邮件内容中入组日期显示为N/A")
            else:
                info("❌ 邮件内容中没有找到入组日期信息")
                
            if "患者年龄：35" in email_content:
                info("✅ 邮件内容中包含正确的患者年龄")
            else:
                info("⚠️ 邮件内容中患者年龄信息不正确")
                
            if "患者性别：男" in email_content:
                info("✅ 邮件内容中包含正确的患者性别")
            else:
                info("⚠️ 邮件内容中患者性别信息不正确")
                
        else:
            info("❌ 未能查询到患者信息")
            
    except Exception as e:
        info(f"测试过程中出错: {e}")


async def test_with_different_patient():
    """测试不同患者的入组日期"""
    info("=== 测试不同患者的入组日期 ===")
    
    # 使用另一个患者的数据
    patient_data = {
        "患者名字": "李四",
        "手机号": "17764016802",  # 李四的手机号
        "是否有不适感": "是",
        "不适感内容": "腰部疼痛",
        "是否需要医生人工和患者联系": "是"
    }
    
    try:
        sender = PatientEmailSender()
        patient_info = await sender.doctor_manager.get_patient_info_from_call_record(patient_data)
        
        if patient_info:
            info(f"李四的患者信息:")
            info(f"  患者编号: {patient_info.get('patient_id')}")
            info(f"  入组日期: {patient_info.get('enrollment_date')}")
            info(f"  训练状态: {patient_info.get('training_status')}")
            info(f"  分配医生: {patient_info.get('assigned_doctor_name')}")
        else:
            info("❌ 未能查询到李四的患者信息")
            
    except Exception as e:
        info(f"测试过程中出错: {e}")


async def main():
    """主测试函数"""
    try:
        await test_email_content_generation()
        print("\n" + "="*60 + "\n")
        await test_with_different_patient()
        print("\n" + "="*60 + "\n")
        await test_enrollment_date_in_email()
        
    except Exception as e:
        info(f"测试过程中出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
