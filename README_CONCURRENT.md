# AI电话机器人 - 并发拨号功能

## 功能概述

本系统新增了并发拨号功能，支持从数据库读取人员名单并进行批量并发拨号。主要特性包括：

- 从MongoDB数据库读取人员信息
- 支持并发控制（使用semaphore控制最大并发数）
- 支持按批次处理，避免系统负载过高
- 支持按训练状态过滤人员
- 个性化问候语（根据姓名生成）
- 详细的拨号进度跟踪

## 配置说明

在 `config.py` 中的 `CONCURRENT_CALL_CONFIG` 配置项：

```python
CONCURRENT_CALL_CONFIG = {
    "max_concurrent_calls": 3,  # 最大并发数
    "retry_times": 3,          # 重试次数
    "retry_interval": 5,       # 重试间隔（秒）
    "call_interval": 2,        # 两次拨号之间的间隔（秒）
    "batch_size": 10,          # 批次大小（每批处理多少个电话）
    "batch_interval": 30,      # 批次间隔（秒）
    "filter_training_status": None,  # 可选，按训练状态过滤人员（如："训练中"）
}
```

### 配置参数说明

- `max_concurrent_calls`: 同时进行的最大通话数，建议设置为3-5，避免系统负载过高
- `batch_size`: 每批处理的电话数量，建议设置为10-20
- `batch_interval`: 批次之间的间隔时间，给系统喘息时间
- `call_interval`: 单个电话之间的间隔，避免拨号过于频繁
- `filter_training_status`: 可以设置为 "训练中"、"暂停"、"未开始" 等来过滤特定状态的人员

## 使用方法

### 1. 准备数据

首先需要在数据库中添加人员信息。可以使用以下几种方式：

#### 方式一：使用测试数据
```python
# 在 main.py 中取消注释以下行
asyncio.run(add_test_personnel())
```

#### 方式二：手动添加人员
```python
from db_utils import db_client

person_data = {
    "name": "张三",
    "phone": "13800138001",
    "age": 45,
    "gender": "男",
    "enrollment_date": "2024-01-15",
    "training_status": "训练中"
}

person_id = await db_client.add_person(person_data)
```

### 2. 运行并发拨号

#### 方式一：直接运行主程序
```bash
python main.py
```

#### 方式二：使用演示脚本
```bash
python demo_concurrent.py
```

### 3. 监控拨号过程

程序会显示详细的拨号进度，包括：
- 从数据库获取的人员数量
- 拨号计划（显示所有将要拨打的电话）
- 批次处理进度
- 单个通话的成功/失败状态

## 系统架构

### 并发控制机制

1. **Semaphore控制**: 使用 `asyncio.Semaphore` 限制最大并发数
2. **批次处理**: 将大量电话分批处理，避免系统过载
3. **独立通话管理**: 每个通话都有独立的 `CallManager` 实例
4. **资源隔离**: 每个通话使用独立的控制器UUID和NATS连接

### 数据流程

```
数据库读取 → 人员过滤 → 批次分割 → 并发拨号 → 通话管理
```

1. 从MongoDB读取人员名单
2. 根据配置过滤特定状态的人员
3. 按批次大小分割人员列表
4. 在每个批次内并发拨号
5. 每个通话独立管理对话流程

## 日志说明

程序会输出详细的日志信息：

- `INFO`: 正常流程信息（拨号计划、批次进度等）
- `WARNING`: 警告信息（拨号失败、数据缺失等）
- `ERROR`: 错误信息（连接失败、异常等）
- `DEBUG`: 调试信息（详细的通话事件等）

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `config.py` 中的 MongoDB 连接配置
   - 确保 MongoDB 服务正在运行

2. **NATS连接失败**
   - 检查 NATS 服务器地址和认证信息
   - 确保网络连接正常

3. **拨号失败**
   - 检查电话号码格式是否正确
   - 检查拨号字符串模板配置
   - 确认电话系统服务正常

4. **并发数过高导致系统不稳定**
   - 降低 `max_concurrent_calls` 设置
   - 增加 `call_interval` 间隔时间

### 性能优化建议

1. **合理设置并发数**: 根据系统性能调整 `max_concurrent_calls`
2. **批次处理**: 对于大量电话，适当设置 `batch_size` 和 `batch_interval`
3. **资源监控**: 监控CPU、内存和网络使用情况
4. **错误重试**: 根据网络情况调整 `retry_times` 和 `retry_interval`

## 扩展功能

可以根据需要扩展以下功能：

1. **拨号结果统计**: 记录成功/失败/无应答等统计信息
2. **拨号调度**: 支持定时拨号、周期性拨号
3. **黑名单管理**: 支持黑名单过滤
4. **拨号优先级**: 支持按优先级排序拨号
5. **实时监控**: Web界面实时监控拨号状态 