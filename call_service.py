"""
独立的拨号服务模块
从main.py中提取拨号相关功能，避免循环依赖
"""

import asyncio
import uuid
import nats
from med_config import NATS_CONFIG, CALL_CONFIG, CONCURRENT_CALL_CONFIG
from rpc_utils import RPCUtils
from speech_handler import SpeechHandler
from call_manager import CallManager
from db_utils import db_client
from log_utils import info, error, debug, warning


async def get_personnel_list(filter_training_status=None):
    """从数据库获取人员名单"""
    try:
        info("正在从数据库获取人员名单...")
        
        # 根据参数或配置过滤训练状态
        if filter_training_status is None:
            filter_training_status = CONCURRENT_CALL_CONFIG.get("filter_training_status")
            
        if filter_training_status:
            info("过滤条件：训练状态 = %s", filter_training_status)
            personnel_list = await db_client.get_all_personnel(training_status=filter_training_status)
        else:
            personnel_list = await db_client.get_all_personnel()
        
        if not personnel_list:
            warning("数据库中没有找到符合条件的人员记录")
            return []
            
        info("从数据库获取到 %d 条人员记录", len(personnel_list))
        
        # 提取姓名和手机号
        call_list = []
        for person in personnel_list:
            name = person.get("name")
            phone = person.get("phone")
            status = person.get("training_status", "未知")
            if name and phone:
                call_list.append({
                    "name": name, 
                    "phone": phone,
                    "training_status": status
                })
            else:
                warning("人员记录缺少姓名或手机号: %s", person)
        
        info("有效的拨号记录: %d 条", len(call_list))

        return call_list
        
    except Exception as e:
        error("获取人员名单失败: %s", e)
        return []


async def get_personnel_by_phone(phone_number):
    """根据手机号获取特定人员信息"""
    try:
        person = await db_client.get_personnel_by_phone(phone_number)
        if person:
            return {
                "name": person.get("name"),
                "phone": person.get("phone"), 
                "training_status": person.get("training_status", "未知")
            }
        return None
    except Exception as e:
        error("根据手机号获取人员信息失败: %s", e)
        return None


async def make_single_call(nats_client, person_info, semaphore, obj_id=None):
    """单个通话任务"""
    async with semaphore:
        name = person_info["name"]
        phone = person_info["phone"]
        
        info("开始拨打电话: %s (%s)", name, phone)
        
        # 为每个通话创建独立的组件
        ctrl_uuid = str(uuid.uuid4())
        rpc_utils = RPCUtils(nats_client, CALL_CONFIG["service"], ctrl_uuid)
        speech_handler = SpeechHandler(rpc_utils)
        call_manager = CallManager(nats_client, rpc_utils, speech_handler, person_info)
        
        try:
            # 启动通话管理器
            await call_manager.start()
            
            # 拨打电话
            success = await call_manager.make_call(phone, obj_id)
            
            if success:
                info("成功拨通 %s (%s)", name, phone)
                # 等待通话完成
                await call_manager.wait_for_call_completion()
                info("通话结束: %s (%s)", name, phone)
                return True
            else:
                warning("拨号失败: %s (%s)", name, phone)
                return False
                
        except Exception as e:
            error("通话过程出错 %s (%s): %s", name, phone, e)
            return False
            
        finally:
            # 清理资源
            call_manager.stop()
            await call_manager.cleanup()


def split_into_batches(personnel_list, batch_size):
    """将人员名单分割成批次"""
    for i in range(0, len(personnel_list), batch_size):
        yield personnel_list[i:i + batch_size]


async def process_batch(batch, batch_num, total_batches):
    """处理一个批次的拨号"""
    info("=== 开始处理第 %d/%d 批次，共 %d 个电话 ===", batch_num, total_batches, len(batch))
    
    max_concurrent = CONCURRENT_CALL_CONFIG.get("max_concurrent_calls", 3)
    semaphore = asyncio.Semaphore(max_concurrent)
    
    nats_client = None
    try:
        # 连接NATS服务器
        nats_client = await nats.connect(NATS_CONFIG["url"])
        
        # 创建批次内的通话任务
        tasks = []
        for person_info in batch:
            task = make_single_call(nats_client, person_info, semaphore)
            tasks.append(task)
        
        # 并发执行批次内的任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        success_count = sum(1 for r in results if r is True)
        info("=== 第 %d/%d 批次处理完成，成功 %d/%d ===", batch_num, total_batches, success_count, len(batch))
        
    except Exception as e:
        error("批次 %d 处理出错: %s", batch_num, e)
        
    finally:
        if nats_client:
            await nats_client.close()


async def concurrent_calls(personnel_list):
    """并发拨打电话"""
    if not personnel_list:
        warning("人员名单为空，无法进行拨号")
        return
        
    max_concurrent = CONCURRENT_CALL_CONFIG.get("max_concurrent_calls", 3)
    batch_size = CONCURRENT_CALL_CONFIG.get("batch_size", 10)
    batch_interval = CONCURRENT_CALL_CONFIG.get("batch_interval", 30)
    
    info("=== 并发拨号配置 ===")
    info("最大并发数: %d", max_concurrent)
    info("批次大小: %d", batch_size)
    info("批次间隔: %d 秒", batch_interval)
    info("总共需要拨打 %d 个电话", len(personnel_list))
    
    # 分割成批次
    batches = list(split_into_batches(personnel_list, batch_size))
    total_batches = len(batches)
    info("分为 %d 个批次进行处理", total_batches)
    
    try:
        for batch_num, batch in enumerate(batches, 1):
            await process_batch(batch, batch_num, total_batches)
            
            # 如果不是最后一个批次，等待批次间隔
            if batch_num < total_batches and batch_interval > 0:
                info("等待 %d 秒后开始下一批次...", batch_interval)
                await asyncio.sleep(batch_interval)
        
        info("🎉 所有拨号任务已完成！")
        
    except Exception as e:
        error("并发拨号过程出错: %s", e)


async def make_immediate_call(phone_number, obj_id=None):
    """立即拨打特定电话号码"""
    try:
        # 获取人员信息
        person_info = await get_personnel_by_phone(phone_number)
        if not person_info:
            error("未找到手机号 %s 对应的人员信息", phone_number)
            return False
            
        info("准备立即拨打电话: %s (%s)", person_info["name"], phone_number)
        
        # 创建信号量（单个通话）
        semaphore = asyncio.Semaphore(1)
        
        # 连接NATS服务器
        nats_client = await nats.connect(NATS_CONFIG["url"])
        
        try:
            # 执行单个通话
            success = await make_single_call(nats_client, person_info, semaphore, obj_id)
            return success
            
        finally:
            await nats_client.close()
            
    except Exception as e:
        error("立即拨号失败: %s", e)
        return False 