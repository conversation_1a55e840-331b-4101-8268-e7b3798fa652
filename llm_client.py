"""
LLM交互模块：负责与大语言模型API的交互
"""

import httpx
import asyncio
import aiohttp
import json
from med_config import get_system_prompt, get_initial_chat_history, LLM_CONFIG

class LLMResponse:
    """LLM响应结果类"""
    def __init__(self, success=True, content="", error_message="", status_code=None):
        self.success = success
        self.content = content
        self.error_message = error_message
        self.status_code = status_code


class LLMClient:
    def __init__(self, config, system_prompt, auto_update_chat=True):
        self.api_key = config["api_key"]
        self.api_base = config["api_base"]
        self.model = config["model"]
        self.system_prompt = system_prompt
        self.chat_history = []
        self.session = None
        self.auto_update_chat = auto_update_chat

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def init_session(self):
        """手动初始化session（如果不使用上下文管理器）"""
        if not self.session:
            self.session = aiohttp.ClientSession()

    async def close(self):
        """手动关闭session"""
        if self.session:
            await self.session.close()
            self.session = None

    def update_prompt_and_history(self, name):
        """
        更新系统提示词和对话历史
        params:
            name: 姓名
        """
        self.system_prompt = get_system_prompt(name)
        self.chat_history = get_initial_chat_history(name)

    def initialize_chat_history(self, initial_history):
        """初始化对话历史"""
        self.chat_history = initial_history.copy()

    async def get_response_stream(self, user_text):
        """
        流式获取LLM回复，返回异步生成器

        使用方式:
        async for chunk in llm.get_response_stream("你好"):
            if chunk.success:
                print(chunk.content, end='', flush=True)
            else:
                print(f"错误: {chunk.error_message}")
                break
        """
        if not self.session:
            yield LLMResponse(False, "", "Session未初始化，请先调用init_session()或使用async with语句")
            return

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 构建消息历史
        messages = [
            {"role": "system", "content": self.system_prompt}
        ]
        messages.extend(self.chat_history)
        messages.append({"role": "user", "content": user_text})
        json_data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            "temperature": 0.1
        }

        try:
            async with self.session.post(f"{self.api_base}", headers=headers, json=json_data, timeout=30) as response:
                if response.status != 200:
                    error_text = await response.text()
                    yield LLMResponse(False, "", f"API调用失败: {response.status} - {error_text}", response.status)
                    return

                # 存储完整消息用于更新历史
                full_message = ""

                # 逐块读取流式响应
                async for chunk in response.content:
                    chunk_str = chunk.decode('utf-8')

                    for line in chunk_str.splitlines():
                        if line.strip():
                            try:
                                line = line.replace('data:', '').strip()
                                if '[DONE]' in line:
                                    continue

                                content_data = json.loads(line)
                                if 'choices' in content_data and len(content_data['choices']) > 0:
                                    delta = content_data['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        full_message += content
                                        yield LLMResponse(True, content)

                            except json.JSONDecodeError as e:
                                yield LLMResponse(False, "", f"JSON解析错误: {line}")
                                continue

                # 更新对话历史
                if full_message and self.auto_update_chat:
                    self.chat_history.append({"role": "user", "content": user_text})
                    self.chat_history.append({"role": "assistant", "content": full_message})

        except asyncio.TimeoutError:
            yield LLMResponse(False, "", "请求超时")
        except Exception as e:
            yield LLMResponse(False, "", f"调用LLM出错: {str(e)}")

    async def add_user_history(self, user_text):
        self.chat_history.append({"role": "user", "content": user_text})

    async def add_ai_history(self, text):
        self.chat_history.append({"role": "assistant", "content": text})

    async def get_last_chat(self, content_only=False):
        if content_only:
            return self.chat_history[-1]['content']
        return self.chat_history[-1]

    async def get_response(self, user_text):
        """
        非流式获取完整回复（兼容原有接口）

        返回:
            LLMResponse对象，包含success, content, error_message, status_code属性
        """
        full_content = ""

        async for chunk in self.get_response_stream(user_text):
            if not chunk.success:
                return chunk  # 直接返回错误
            full_content += chunk.content

        return LLMResponse(True, full_content)

# 使用示例 ---------------------------------------
async def main():
    async with LLMClient(LLM_CONFIG, get_system_prompt('s')) as llm:

        # 方式1：流式处理
        print("流式输出:")
        async for chunk in llm.get_response_stream("1+1等于多少？请详细解释"):
            if chunk.success:
                print(chunk.content, end='', flush=True)
            else:
                print(f"\n错误: {chunk.error_message}")
                if chunk.status_code:
                    print(f"状态码: {chunk.status_code}")
                break
        print("\n" + "="*50)

        # 方式2：获取完整回复
        print("完整回复:")
        response = await llm.get_response("2+2等于多少？")
        if response.success:
            print(response.content)
        else:
            print(f"错误: {response.error_message}")
            if response.status_code:
                print(f"状态码: {response.status_code}")

# 并发调用示例
async def concurrent_example():
    async with LLMClient(LLM_CONFIG, get_system_prompt('s')) as llm:

        # 并发处理多个请求
        async def process_request(question):
            response = await llm.get_response(question)
            if response.success:
                return f"Q: {question}\nA: {response.content}\n"
            else:
                return f"Q: {question}\nError: {response.error_message}\n"

        questions = ["1+1=?", "2+2=?", "3+3=?"]
        results = await asyncio.gather(*[process_request(q) for q in questions])

        for result in results:
            print(result)

if __name__ == '__main__':
    asyncio.run(main())
    # asyncio.run(concurrent_example())