"""
测试立即拨号功能
"""

from scheduler_manager import make_immediate_call


def test_immediate_call():
    """测试立即拨号功能"""
    print("=== 测试立即拨号功能 ===")
    
    # 使用您提供的测试号码
    test_phone = "18971492577"
    
    print(f"正在测试立即拨打电话: {test_phone}")
    
    try:
        result = make_immediate_call(test_phone)
        print(f"拨号结果: {result}")
        
        if result["success"]:
            print("✅ 立即拨号功能正常")
        else:
            print(f"❌ 立即拨号失败: {result['message']}")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")


if __name__ == "__main__":
    test_immediate_call() 