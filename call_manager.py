"""
通话管理器：负责管理通话状态、处理通话事件和协调语音处理

注意ctrl_uuid 和 call_uuid区别 call_uuid是在dial拨号后产生的，在asr和tts时都需加上
"""

import json
import asyncio
import uuid
from datetime import datetime
import time

from bson import ObjectId

from AI_response import AIResponse, extract_conversation_info
from med_config import CALL_CONFIG, LLM_CONFIG, get_system_prompt, get_initial_chat_history
from db_utils import CurrentRecord
from rpc_utils import RPCUtils
from speech_handler import SpeechHandler
from llm_client import LLMClient
from log_utils import debug, info, warning, error, filter_print_xcc_events
import nats

from tools.weixin_send import send_patient_report_async
from tools.patient_email_sender import send_patient_report_email_async


class CallManager:
    def __init__(self, nats_client, rpc_utils: RPCUtils, speech_handler: SpeechHandler, person_info=None):
        self.nats_client = nats_client
        self.rpc_utils = rpc_utils
        self.speech_handler = speech_handler
        self.person_info = person_info or {}  # 人员信息（姓名、电话等）
        self.person_name = self.person_info.get("name", "患者")
        self.phone_number = self.person_info.get("phone", "")
        
        self.call_uuid = None
        self.call_state = "IDLE"  # IDLE, DIALING, RINGING, ANSWERED, HANGUP
        self.running = True
        self.call_start_time = None  # 通话开始时间（接通时记录）
        self.call_duration_monitor_task = None  # 通话时长监控任务
        self.max_call_duration = 7 * 60  # 最大通话时长：7分钟 = 420秒

        self.ai_response = AIResponse(self.rpc_utils,  self.person_info)
        self.speech_handler.set_ai_response(self.ai_response)
        self.speech_handler.set_call_manager(self)

        self.current_record = CurrentRecord()


    async def start(self):
        """启动通话管理器"""
        info("启动通话管理器...")
        
        # 初始化LLM客户端会话
        await self.ai_response.init_session()
        
        # 订阅语音识别事件 - 使用控制器专属的subject
        ctrl_uuid = self.rpc_utils.ctrl_uuid
        detection_subject = f"cn.xswitch.ctrl.{ctrl_uuid}"
        await self.nats_client.subscribe(detection_subject, cb=self._handle_call_event)
        # 订阅通话状态事件 - 添加通配符订阅以确保能收到所有事件
        # await self.nats_client.subscribe("cn.xswitch.ctrl", cb=self._handle_call_event)
        # await self.nats_client.subscribe("cn.xswitch.ctrl.*", cb=self._handle_call_event)

        # await self.nats_client.subscribe(detection_subject, cb=self.speech_handler.handle_speech_event)
        
        info("事件订阅完成（包括语音识别事件: %s）", detection_subject)


    async def make_call(self, phone_number, obj_id=None):
        """主动拨打电话"""
        info("准备拨打电话: %s", phone_number)
        self.call_state = "DIALING"
        self.call_uuid = str(uuid.uuid4())
        self.phone_number = phone_number

        if obj_id is None:
            doc_id = str(ObjectId())
        else:
            doc_id = obj_id

        await self.current_record.init_call_record({
            "_id": doc_id,
            "患者名字":self.person_name,
            "手机号":phone_number,
            "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
            "记录日期": datetime.now().strftime("%Y-%m-%d"),
            "拨号状态": "拨号中"
        })
        
        # 构建拨号字符串
        dial_string = f"sofia/gateway/out/tyscg{phone_number}"
        # dial_string = f"user/1001"
        

        _, response = await self.rpc_utils.dial(dial_string, call_uuid=self.call_uuid, timeout=CALL_CONFIG["timeout"])
        error(f"self.call_uuid : {self.call_uuid}")

        if response.get("result", {}).get("code") == 200:
            info("拨号请求成功，等待接通... UUID: %s", self.call_uuid)
            await self.ai_response.init()
            return True
        else:
            warning("拨号失败: %s", response)
            self.call_state = "IDLE"
            await self.current_record.update_call_status('无法接通')
            return False
    
    async def _handle_call_event(self, msg):
        """处理通话事件"""
        try:
            message_data = msg.data.decode()
            message = json.loads(message_data)
            
            # 增加调试信息
            if not filter_print_xcc_events(message, ["Event.DetectedData"]):
                debug("=== 收到NATS消息 ===")
                debug("Subject: %s", msg.subject)
                debug("消息内容: %s", message_data)
                debug("当前通话Call UUID: %s", self.call_uuid)
                debug("当前状态: %s", self.call_state)
                debug("=== 结束NATS消息 ===")
            
            if "method" in message and message["method"] == "Event.Channel":
                await self._handle_channel_event(message)
            elif "method" in message and message["method"] == "Event.DetectedData": # 异步语音识别结果事件，当识别rpc里不传入media参数时触发，需要在xswitch系统页面的对接-xcc-binding里面启用才能收到
                await self.speech_handler.handle_speech_event(message)
            elif "method" in message and message["method"] == "Event.Play": # tts播放完毕可以收到，只有在使用DetectSpeech且带有media，即同步形式的asr才会触发，普通tts play不会触发
                await self.speech_handler.handle_tts_finish_event(message)
            else:
                debug("收到非Event.Channel消息: ")
                # debug(message.get('method', 'Unknown'))
                
        except Exception as e:
            error("处理通话事件出错: %s", e)
            import traceback
            traceback.print_exc()
    
    async def _handle_channel_event(self, message):
        """处理Channel事件"""
        if "params" not in message:
            warning("⚠️ Event.Channel消息缺少params字段")
            return
            
        params = message["params"]
        event_uuid = params.get("uuid")
        state = params.get("state")
        direction = params.get("direction", "")
        answered = params.get("answered", False)
        
        debug("====== 📞 Channel事件详情:  ====== ")
        debug("   UUID: %s", event_uuid)
        debug("   状态: %s", state)
        debug("   方向: %s", direction)
        debug("   已应答: %s", answered)
        debug("   当前管理的UUID: %s", self.call_uuid)
        debug("   UUID匹配: %s", event_uuid == self.call_uuid)
        debug("====== 结束Channel事件详情:  ====== ")
        
        # 处理新的来电（UUID不匹配且是START状态的来电）
        if state == "START" and direction == "inbound" and event_uuid != self.call_uuid:
            if self.call_state == "IDLE":
                info("🔔 检测到新的来电")
                await self.handle_incoming_call(event_uuid)
                return
            else:
                info("📞 当前已有通话（状态：%s），忽略新来电", self.call_state)
                return
        
        # 处理当前通话的事件 - 放宽UUID匹配条件
        is_current_call = (event_uuid == self.call_uuid) or (self.call_uuid is None and state == "START")
        
        if not is_current_call:
            debug("🔍 忽略其他通话的事件 (UUID: %s)", event_uuid)
            debug(f"Call UUID: {self.call_uuid}")
            debug(f"Ctrl UUID: {self.rpc_utils.ctrl_uuid}")
            return
            
        info("✅ 处理当前通话事件 - 状态: %s", state)
        
        # 如果是START状态且我们还没有设置UUID，则设置它
        if state == "START" and self.call_uuid is None:
            info("🎯 设置通话UUID: %s", event_uuid)
            self.call_uuid = event_uuid
        
        if state == "START":
            info("🚀 通话开始")
            if direction == "outbound":
                info("📱 主动拨号已开始")
                self.call_state = "DIALING"
                
        elif state == "RINGING" or state == "CALLING":
            info("📱 电话振铃中...")
            self.call_state = "RINGING"
            
        elif state == "ANSWERED":
            info("🎉 电话已接通！")
            self.call_state = "ANSWERED"
            await self._on_call_answered()
            
        elif state == "HANGUP":
            info("📴 通话已挂断")
            self.call_state = "HANGUP"
            await self._on_call_hangup()
            
        elif state == "DESTROY":
            info("💥 通话销毁，程序将结束")
            self.call_state = "DESTROY"
            # self.running = False
            await self._on_call_hangup()
            
        elif state == "EARLY":
            info("⏰ 收到早期媒体")
            
        elif state == "UNBRIDGE":
            info("🔗 通话取消桥接")
            
        else:
            if state:
                debug("❓ 收到未处理的状态: %s", state)
            
        # 处理应答状态变化（这个比state变化更准确）
        # if answered and self.call_state not in ["ANSWERED", "HANGUP", "DESTROY"]:
        #     info("✅ 通过answered字段检测到通话已应答")
        #     self.call_state = "ANSWERED"
        #     await self._on_call_answered()
    
    async def _on_call_answered(self):
        """电话接通后的处理"""
        info("电话接通，开始业务流程...")

        # 记录通话开始时间
        self.call_start_time = time.time()
        info(f"通话开始时间记录: {datetime.fromtimestamp(self.call_start_time).strftime('%Y-%m-%d %H:%M:%S')}")

        # 启动通话时长监控任务
        self.call_duration_monitor_task = asyncio.create_task(self._monitor_call_duration())
        info(f"启动通话时长监控，最大通话时长: {self.max_call_duration//60}分钟")

        update_res = await self.current_record.update_call_status('已接听')
        debug(f'电话接通 update_call_status: {update_res}')

        # 设置语音处理器的Channel UUID
        self.speech_handler.set_channel_uuid(self.call_uuid)
        current_tts_task = asyncio.create_task(self.speech_handler.talk_loop())


        # 根据人员信息生成个性化问候语
        # person_name = self.person_info.get("name", "患者")
        # init_response = f"您好，我是武汉协和医院骨科的智能助理，请问是{person_name}吗？"
        # response = init_response
        # while self.running:
        #     asr_res = await self.rpc_utils.xnode_detect(self.call_uuid, response)
        #     response, status = await self.parse_detect_res(asr_res)
        #     if status != 0 : # 语音识别结果状态异常，则继续下一轮语音识别但是不播放TTS
        #         response = ''

    async def parse_detect_res(self, res):
        """
        解析语音识别结果
        params:

            res: 识别结果
        returns:
            根据识别结果返回不同的响应文本
        """

        if res == None:
            return None, -1

        elif res["result"]["code"] == 408: #用户一直没说话
            return "我这边还没收到您的回复", -1

        elif res["result"]["code"] > 300:
            # await xnode_hangup(service, uuid)
            self.stop()
            await self.rpc_utils.hangup(self.call_uuid)
            warning(f'异常语音识别状态码: {res["result"]["code"]}')
            return None, -1

        elif res["result"]["code"] == 200:
            if "result" in res and "data" in res["result"] and "text" in res["result"]["data"]:
                user_text = res["result"]["data"]["text"]
                debug(f'语音识别结果: {user_text}')
                if user_text == '':
                    return "我这边还没收到您的回复", 0

                # 调用 LLM 获取回复
                llm_response, code = self.ai_response.get_response(user_text)
                if code != 0:
                    # 如果LLM调用失败，返回默认回复
                    self.stop()
                    await self.rpc_utils.hangup(self.call_uuid)



        else:
            warning(f'未捕捉的语音识别状态码: {res["result"]["code"]}')
            return None, -1

    async def _monitor_call_duration(self):
        """后台监控通话时长，超过最大时长自动挂断"""
        try:
            while self.call_state == "ANSWERED" and self.running and self.call_start_time:
                await asyncio.sleep(30)  # 每30秒检查一次

                # 检查通话状态是否仍然有效
                if self.call_state != "ANSWERED" or not self.running:
                    debug("通话状态已改变，停止时长监控")
                    break

                # 计算当前通话时长
                current_duration = time.time() - self.call_start_time
                debug(f"当前通话时长: {int(current_duration)}秒 / {self.max_call_duration}秒")

                # 检查是否超过最大通话时长
                if current_duration >= self.max_call_duration:
                    info(f"通话时长超过{self.max_call_duration//60}分钟，准备主动挂断")
                    await self._handle_call_timeout()
                    break

        except asyncio.CancelledError:
            debug("通话时长监控任务被取消")
        except Exception as e:
            error(f"通话时长监控出错: {e}")

    async def _handle_call_timeout(self):
        """处理通话超时情况"""
        try:
            info("通话时间已到，准备结束通话")

            # 播放结束提示音（可选）
            timeout_message = "通话时间已到，感谢您的配合，再见"
            try:
                # 尝试播放提示音，但不等待完成，避免延长通话时间
                await self.rpc_utils.play_tts(self.call_uuid, timeout_message)
                # 给一点时间让提示音开始播放
                await asyncio.sleep(2)
            except Exception as e:
                warning(f"播放超时提示音失败: {e}")

            # 主动挂断电话
            info("执行主动挂断")
            await self.rpc_utils.hangup(self.call_uuid)

        except Exception as e:
            error(f"处理通话超时失败: {e}")
            # 即使出错也要尝试挂断
            try:
                await self.rpc_utils.hangup(self.call_uuid)
            except:
                pass

    async def _on_call_hangup(self):
        """通话挂断后的处理"""
        info("🧹 清理通话资源...")

        # 取消通话时长监控任务
        if self.call_duration_monitor_task and not self.call_duration_monitor_task.done():
            self.call_duration_monitor_task.cancel()
            try:
                await self.call_duration_monitor_task
            except asyncio.CancelledError:
                debug("通话时长监控任务已取消")
            except Exception as e:
                warning(f"取消监控任务时出错: {e}")
            finally:
                self.call_duration_monitor_task = None

        # 计算通话时长
        call_duration_seconds = 0
        if self.call_start_time is not None:
            call_end_time = time.time()
            call_duration_seconds = int(call_end_time - self.call_start_time)
            info(f"通话结束时间: {datetime.fromtimestamp(call_end_time).strftime('%Y-%m-%d %H:%M:%S')}")
            info(f"通话时长: {call_duration_seconds}秒")
        else:
            info("通话未正常接通，时长为0秒")

        if self.current_record.call_status == "已接听":
            status = '通话结束'
        else:
            status = '无法接通'

        # 将这次通话中大模型的回复作为模板存入数据库
        await self.ai_response.doc_cache.flush_chat_tuple()
        warning(f"self.ai_response.llm_client.chat_history {self.ai_response.llm_client.chat_history}")
        if len(self.ai_response.llm_client.chat_history) > 1: # 对话历史记录默认有初始化开场白，至少有一个用户回复才记录
            await self.current_record.update_call_record_chat_history(self.ai_response.llm_client.chat_history)
        else:
            status = '未接听'

        # 更新通话状态和通话时长
        await self.current_record.update_call_status(status)
        await self.current_record.update_call_duration(call_duration_seconds)
        debug(f'电话挂断 update_call_status: {status}, 通话时长: {call_duration_seconds}秒')

        if len(self.ai_response.llm_client.chat_history) > 1:
            debug('启动大模型对话信息抽取')
            ext_res = await extract_conversation_info(self.ai_response.llm_client.chat_history, self.ai_response.llm_client)
            if ext_res is not None:
                info('对话抽取结果：' + str(ext_res))
                await self.current_record.update_call_record(ext_res)
                if ext_res['是否有不适感'] == '是' or ext_res['是否需要医生人工和患者联系'] == '是':
                    # 准备邮件数据，包含对话历史
                    email_data = ext_res.copy()
                    email_data.update({
                        "患者名字": self.person_name,
                        "手机号": self.phone_number,
                        '通话时间': datetime.now(),
                        '对话历史记录': self.ai_response.llm_client.chat_history
                    })

                    # 发送微信通知
                    # result = await send_patient_report_async(ext_res)
                    # info(f"微信发送结果: {result}")
                    # if result["success"]:
                    #     info("✅ 微信消息发送成功")
                    # else:
                    #     warning(f"❌ 微信消息发送失败: {result['error']}")
                    #     if result["response_data"]:
                    #         warning(f"响应数据: {result['response_data']}")

                    # 发送邮件通知（包含对话历史）
                    email_result = await send_patient_report_email_async(email_data)
                    info(f"邮件发送结果: {email_result}")
                    if email_result["success"]:
                        info("✅ 邮件发送成功（包含对话历史）")
                    else:
                        warning(f"❌ 邮件发送失败: {email_result['error']}")
                        if email_result.get("details"):
                            warning(f"邮件发送详情: {email_result['details']}")

            else:
                await self.current_record.update_call_record({'大模型调用情况':'失败'})

        # 停止语音识别
        await self.speech_handler.stop_listening()
        
        # 关闭LLM客户端会话
        await self.ai_response.release_session()
        
        # 重置状态
        self.call_state = "IDLE"
        self.call_uuid = None
        
        # 设置程序结束标志
        info("🏁 通话已结束，程序将在清理完成后退出")
        self.running = False
        if self.current_record.call_status == "通话结束":
            update_res = await  self.current_record.update_call_status('通话结束-信息解析完成')
            debug(f'电话接通 update_call_status: {update_res}')




    
    async def wait_for_call_completion(self):
        """等待通话完成"""
        info("⏳ 等待通话完成...")
        
        while self.running:
            await asyncio.sleep(1)
            
            # 检查通话状态
            if self.call_state in ["HANGUP", "DESTROY", "IDLE"] and not self.running:
                debug("📋 通话状态: %s, 运行状态: %s", self.call_state, self.running)
                break
                
            # 定期显示状态（每10秒一次）
            if hasattr(self, '_status_counter'):
                self._status_counter += 1
            else:
                self._status_counter = 1
                
            if self._status_counter % 10 == 0:
                debug("📊 当前状态: %s, 运行中: %s", self.call_state, self.running)
                
        info("✅ 通话管理器结束")
    
    def stop(self):
        """停止通话管理器"""
        self.running = False
        
    async def cleanup(self):
        await self.ai_response.release_session()
    
    async def handle_incoming_call(self, call_uuid):
        """处理来电（当接收到START事件时调用）"""
        info("处理来电，UUID: %s", call_uuid)
        self.call_uuid = call_uuid
        self.call_state = "INCOMING"
        
        try:
            # 根据文档要求：在收到START消息后10秒内接管呼叫
            info("接管来电...")
            await self.rpc_utils.accept(call_uuid)
            
            # 短暂延迟后应答
            await asyncio.sleep(0.5)
            info("应答来电...")
            await self.rpc_utils.answer(call_uuid)
            
        except Exception as e:
            error("处理来电失败: %s", e)
            await self.rpc_utils.hangup(call_uuid) 