"""
API使用示例
展示如何在Flask应用中使用调度器管理功能
"""

import time
from scheduler_manager import (
    start_scheduler,
    stop_scheduler,
    restart_scheduler,
    get_scheduler_status,
    refresh_personnel,
    make_immediate_call
)


def demo_scheduler_management():
    """演示调度器管理功能"""
    print("=== 调度器管理演示 ===")
    
    # 1. 获取初始状态
    print("\n1. 获取初始状态:")
    status = get_scheduler_status()
    print(f"状态: {status}")
    
    # 2. 启动调度器
    print("\n2. 启动调度器:")
    result = start_scheduler()
    print(f"启动结果: {result}")
    
    if result["success"]:
        # 3. 等待一下，然后获取状态
        print("\n3. 等待5秒后获取状态:")
        time.sleep(5)
        status = get_scheduler_status()
        print(f"状态: {status}")
        
        # 4. 刷新人员名单
        # print("\n4. 刷新人员名单:")
        # refresh_result = refresh_personnel()
        # print(f"刷新结果: {refresh_result}")
        #
        # # 5. 重启调度器
        # print("\n5. 重启调度器:")
        # restart_result = restart_scheduler()
        # print(f"重启结果: {restart_result}")
        #
        # # 6. 最后停止调度器
        # print("\n6. 停止调度器:")
        # time.sleep(2)
        # stop_result = stop_scheduler()
        # print(f"停止结果: {stop_result}")


def demo_immediate_call():
    """演示立即拨号功能"""
    print("\n=== 立即拨号演示 ===")
    
    # 这里使用一个测试号码，实际使用时请替换为真实号码
    test_phone = "18971492577"
    
    print(f"尝试立即拨打电话: {test_phone}")
    result = make_immediate_call(test_phone)
    print(f"拨号结果: {result}")


def flask_app_example():
    """
    Flask应用集成示例
    这是一个示例，展示如何在Flask应用中使用这些功能
    """
    print("\n=== Flask应用集成示例 ===")
    print("""
    以下是如何在Flask应用中使用这些API的示例代码：
    
    ```python
    from flask import Flask, jsonify, request
    from scheduler_manager import (
        start_scheduler,
        stop_scheduler,
        restart_scheduler,
        get_scheduler_status,
        refresh_personnel,
        make_immediate_call
    )
    
    app = Flask(__name__)
    
    @app.route('/api/scheduler/start', methods=['POST'])
    def api_start_scheduler():
        result = start_scheduler()
        return jsonify(result)
    
    @app.route('/api/scheduler/stop', methods=['POST'])
    def api_stop_scheduler():
        result = stop_scheduler()
        return jsonify(result)
    
    @app.route('/api/scheduler/restart', methods=['POST'])
    def api_restart_scheduler():
        result = restart_scheduler()
        return jsonify(result)
    
    @app.route('/api/scheduler/status', methods=['GET'])
    def api_get_scheduler_status():
        result = get_scheduler_status()
        return jsonify(result)
    
    @app.route('/api/scheduler/refresh', methods=['POST'])
    def api_refresh_personnel():
        result = refresh_personnel()
        return jsonify(result)
    
    @app.route('/api/call/immediate', methods=['POST'])
    def api_make_immediate_call():
        data = request.get_json()
        phone_number = data.get('phone')
        if not phone_number:
            return jsonify({"success": False, "message": "缺少手机号参数"})
        
        result = make_immediate_call(phone_number)
        return jsonify(result)
    
    if __name__ == '__main__':
        app.run(debug=True)
    ```
    """)


if __name__ == "__main__":
    print("AI电话机器人 - 调度器管理API示例")
    
    # 运行调度器管理演示
    demo_scheduler_management()
    
    # 运行立即拨号演示（注释掉以避免实际拨号）
    demo_immediate_call()
    
    # 显示Flask集成示例
    # flask_app_example()
    
    print("\n演示完成！") 