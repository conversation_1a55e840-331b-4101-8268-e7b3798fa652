# 数据库结构分析报告

> 生成时间: 2025-06-21 12:59:34
> 数据库名称: med_call_records

## 概述

本报告基于实际数据库内容分析生成，包含了 med_call_records 数据库中所有集合的结构信息。

## 集合概览

| 集合名称 | 文档数量 | 分析样本数 |
|---------|---------|----------|
| `call_records` | 155 | 5 |
| `reply_cache` | 1 | 1 |
| `reply_cache_str` | 1 | 1 |
| `exercise_plans` | 8 | 5 |
| `sys_info` | 7 | 5 |
| `personnel` | 10 | 5 |

## 集合: call_records

**文档总数**: 155

**分析样本数**: 5

### 字段结构

| 字段名 | 数据类型 | 示例值 |
|-------|---------|--------|
| `_id` | ObjectId | `ObjectId`, `ObjectId`, `ObjectId` |
| `updated_at` | datetime | `datetime`, `datetime`, `datetime` |
| `不适感内容` | str | `腰部有些酸胀` |
| `创建时间` | datetime | `datetime`, `datetime`, `datetime` |
| `对话历史记录` | list | `[数组，长度: 1]`, `[数组，长度: 1]`, `[数组，长度: 8]` |
| `患者名字` | str | `张三`, `张三`, `张三` |
| `患者编号` | str | `P0001`, `P0001`, `P0001` |
| `手机号` | str | `***********`, `***********`, `***********` |
| `拨号状态` | str | `拨号中`, `无法接通`, `通话结束` |
| `是否有不适感` | str | `是` |
| `是否需要医生人工和患者联系` | str | `是` |
| `训练完成情况` | str | `完成` |
| `训练时长` | str | `30分钟` |
| `训练次数` | str | `300次` |
| `记录日期` | str | `2025-06-05`, `2025-06-05`, `2025-06-04` |
| `通话时间` | str | `2025-06-05 10:40`, `2025-06-05 21:16`, `2025-06-04 09:56` |
| `锻炼辅助仪器是否有问题` | str | `否` |
| `锻炼辅助仪器问题内容` | str | `` |

### 示例文档

#### 示例文档 1

```json
{
  "_id": "6841038f24953c4c3a058f6f",
  "患者名字": "张三",
  "手机号": "***********",
  "通话时间": "2025-06-05 10:40",
  "记录日期": "2025-06-05",
  "拨号状态": "拨号中",
  "创建时间": "2025-06-05T10:40:15.265000",
  "对话历史记录": [
    {
      "role": "assistant",
      "content": "您好，我是武汉协和医院骨科的智能助理，请问是张三吗？"
    }
  ],
  "updated_at": "2025-06-19T12:47:00.737000",
  "患者编号": "P0001"
}
```

#### 示例文档 2

```json
{
  "_id": "684198cb95d0ebc33c9336c0",
  "患者名字": "张三",
  "手机号": "***********",
  "通话时间": "2025-06-05 21:16",
  "记录日期": "2025-06-05",
  "拨号状态": "无法接通",
  "创建时间": "2025-06-05T21:16:59.650000",
  "对话历史记录": [
    {
      "role": "assistant",
      "content": "您好，我是武汉协和医院骨科的智能助理，请问是张三吗？"
    }
  ],
  "updated_at": "2025-06-19T12:47:00.740000",
  "患者编号": "P0001"
}
```

---

## 集合: reply_cache

**文档总数**: 1

**分析样本数**: 1

### 字段结构

| 字段名 | 数据类型 | 示例值 |
|-------|---------|--------|
| `_id` | str | `dialogue_cache` |
| `question_templates` | list | `[数组，长度: 22]` |
| `stats` | dict | `[对象，键数: 2]` |

### 示例文档

#### 示例文档 1

```json
{
  "_id": "dialogue_cache",
  "question_templates": [
    {
      "id": "67e288de",
      "question_pattern": "您好[，,]?我是武汉协和医院骨科的智能助理[，,]?请问是([\\u4e00-\\u9fa5]{2,6})吗[？?]?",
      "reply_templates": [
        {
          "id": "23136ea3",
          "user_reply_pattern": "^(是+|对+|嗯+|恩+|没错|正确|好|行|可以|确认|确定|(是的)+|(对的)+|(是我)+|就是我|我就是|(我是)+|确实|没问题|好的|(对啊)+|(是啊)+|嗯啊|OK|ok|中|(对头)+|没毛病)$",
          "next_question": "{name}，请问您昨天进行锻炼了吗？",
          "variables": {
            "name": "$1"
          },
          "hit_count": 0
        },
        {
          "id": "e489d52c",
          "user_reply_pattern": ".*(不是|不对|我不是|他不在|她不在|找错了|打错了|不是本人|不是我)",
          "next_question": "好的，请{name}本人接一下电话好吗？",
          "variables": {
            "name": "$1"
          },
          "hit_count": 0
        }
      ],
      "tags": [
        "identity_confirm",
        "positive"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.413000"
    },
    {
      "id": "e850edf8",
      "question_pattern": "您好[，,]?我是武汉协和医院骨科的智能助理[，,]?请问是([\\u4e00-\\u9fa5]{2,6})吗[？?]?",
      "reply_templates": [
        {
          "id": "6b3419a6",
          "user_reply_pattern": ".*(不是|不对|我不是|他不在|她不在|找错了|打错了|不是本人|不是我)",
          "next_question": "好的，请{name}本人接一下电话好吗？",
          "variables": {
            "name": "$1"
          },
          "hit_count": 0
        }
      ],
      "tags": [
        "identity_confirm",
        "not_patient"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.414000"
    },
    {
      "id": "307cab1e",
      "question_pattern": "请问您昨天进行锻炼了吗[？?]?",
      "reply_templates": [
        {
          "id": "77bfd5f5",
          "user_reply_pattern": "(是|对|嗯|已经|完成了|锻炼了|做了|进行了|练了|训练了)",
          "next_question": "您昨天锻炼了多少下？",
          "variables": {},
          "hit_count": 0
        },
        {
          "id": "b1f2c9df",
          "user_reply_pattern": "(没有|没|不|未|忘了|没时间|没做|没进行|没练|没训练)",
          "next_question": "请问昨天没有锻炼是什么原因呢？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "exercise_status",
        "did_exercise"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.414000"
    },
    {
      "id": "c5ea2c5d",
      "question_pattern": "请问您昨天进行锻炼了吗[？?]?",
      "reply_templates": [
        {
          "id": "84e8885d",
          "user_reply_pattern": "(没有|没|不|未|忘了|没时间|没做|没进行|没练|没训练)",
          "next_question": "请问昨天没有锻炼是什么原因呢？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "exercise_status",
        "no_exercise"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.415000"
    },
    {
      "id": "072a769f",
      "question_pattern": "您昨天锻炼了多少(?:下|次)[？?]?",
      "reply_templates": [
        {
          "id": "b5029738",
          "user_reply_pattern": ".*?(?:我?昨天?锻炼了?|做了|完成了|练了)?(\\d+)(?:下|次)",
          "next_question": "跟您确认下，您是昨天锻炼了{count}下，是吗？",
          "variables": {
            "count": "$1"
          },
          "hit_count": 0
        }
      ],
      "tags": [
        "exercise_count"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.415000"
    },
    {
      "id": "38487a98",
      "question_pattern": "跟您确认下，您是昨天锻炼了(\\d+)下，是吗[？?]?",
      "reply_templates": [
        {
          "id": "91e7b58e",
          "user_reply_pattern": "^(是|对|嗯|恩|没错|正确|确认|确定|是的|对的|没问题|对啊|是啊|好的)",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "exercise_count_confirm",
        "correct"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.415000"
    },
    {
      "id": "df225148",
      "question_pattern": "您昨天总共花了多长时间完成锻炼[？?]?",
      "reply_templates": [
        {
          "id": "69b8c836",
          "user_reply_pattern": ".*?(?:大约|差不多|约|大概|花了|用了|总共)?(\\d+(?:\\.\\d+)?)\\s*(分钟|小时|个小时)",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "variables": {
            "time": "$1",
            "unit": "$2"
          },
          "hit_count": 0
        },
        {
          "id": "9ad4b1c5",
          "user_reply_pattern": ".*?(?:大约|差不多|约|大概|花了|用了|总共)?(一|二|三|四|五|六|七|八|九|十|十一|十二|十三|十四|十五|二十|三十|四十|五十|六十|七十|八十|九十|一百)(?:多)?分钟",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "exercise_duration"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.416000"
    },
    {
      "id": "4f2200e5",
      "question_pattern": "您昨天总共花了多长时间完成锻炼[？?]?",
      "reply_templates": [
        {
          "id": "53d1cd13",
          "user_reply_pattern": ".*?(?:大约|差不多|约|大概|花了|用了|总共)?(一|二|三|四|五|六|七|八|九|十|十一|十二|十三|十四|十五|二十|三十|四十|五十|六十|七十|八十|九十|一百)(?:多)?分钟",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "exercise_duration",
        "chinese_numbers"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.416000"
    },
    {
      "id": "1e128037",
      "question_pattern": "训练后感觉怎么样.*(?:有没有感到任何不适|感觉如何)[？?]?",
      "reply_templates": [
        {
          "id": "eb53acb8",
          "user_reply_pattern": "^(很好|挺好|还好|不错|感觉好|没有不适|没有|没|没什么不适|没问题|正常|舒服|挺舒服|感觉正常|没什么感觉)(?!.*(酸|痛|疼|不舒服|不适|难受|胀|累|疲劳))",
          "next_question": "很好，您使用的辅助仪器都还正常吧？",
          "variables": {},
          "hit_count": 0
        },
        {
          "id": "e1e186ab",
          "user_reply_pattern": ".*(腰|腰部|后腰|腰背).*(酸|痛|疼|不舒服|不适|胀|累|疲劳|沉|重)",
          "next_question": "我了解您的情况了。这种腰部酸感在锻炼完休息后有没有缓解呢？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "exercise_feeling",
        "no_discomfort"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.416000"
    },
    {
      "id": "5c3a1461",
      "question_pattern": "训练后感觉怎么样.*(?:有没有感到任何不适|感觉如何)[？?]?",
      "reply_templates": [
        {
          "id": "419b7b1e",
          "user_reply_pattern": ".*(腰|腰部|后腰|腰背).*(酸|痛|疼|不舒服|不适|胀|累|疲劳|沉|重)",
          "next_question": "我了解您的情况了。这种腰部酸感在锻炼完休息后有没有缓解呢？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "exercise_feeling",
        "waist_discomfort"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.417000"
    },
    {
      "id": "7043514d",
      "question_pattern": "这种.*(?:酸感|不适感|疼痛)在锻炼完休息后有没有缓解呢[？?]?",
      "reply_templates": [
        {
          "id": "5ed74d86",
          "user_reply_pattern": ".*(缓解了|好了|没事了|恢复了|不酸了|不痛了|消失了|减轻了|好多了|舒服了|正常了)",
          "next_question": "很好，这是正常的锻炼反应，说明锻炼起到了效果。您使用的辅助仪器都还正常吧？",
          "variables": {},
          "hit_count": 0
        },
        {
          "id": "58eb8b8d",
          "user_reply_pattern": ".*(没有缓解|一直|持续|还在|没好|还酸|还痛|还疼|还不舒服|到现在还).*(?:没缓解)?",
          "next_question": "这种不适感是今天才出现的，还是之前就有？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "discomfort_relief",
        "relieved"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.417000"
    },
    {
      "id": "e392d57d",
      "question_pattern": "这种.*(?:酸感|不适感|疼痛)在锻炼完休息后有没有缓解呢[？?]?",
      "reply_templates": [
        {
          "id": "cb9ac467",
          "user_reply_pattern": ".*(没有缓解|一直|持续|还在|没好|还酸|还痛|还疼|还不舒服|到现在还).*(?:没缓解)?",
          "next_question": "这种不适感是今天才出现的，还是之前就有？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "discomfort_relief",
        "not_relieved"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.417000"
    },
    {
      "id": "8dee4052",
      "question_pattern": "这种.*(?:不适感|酸感|疼痛)是今天才出现的，还是之前就有[？?]?",
      "reply_templates": [
        {
          "id": "b90df758",
          "user_reply_pattern": ".*(之前|以前|一直|本来|原来|早就|老早就).*就?有",
          "next_question": "好的，请问您是否向我们的医生反应过这个情况？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "discomfort_history",
        "existing_symptom"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.418000"
    },
    {
      "id": "f43d650d",
      "question_pattern": "请问您?是否向我们的医生反应过.*(?:这个情况|这种不适)[？?]?",
      "reply_templates": [
        {
          "id": "0e5c931d",
          "user_reply_pattern": "^(没有|没|不|未|还没有|忘了|没说|没告诉|没反应)",
          "next_question": "好的，我会把您的情况反馈给医生，他会在12小时内联络您。鉴于您本次训练结束后仍有不适感，建议您在医生给您下一步训练建议前，暂停训练。",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "doctor_feedback",
        "not_reported"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.418000"
    },
    {
      "id": "efadffba",
      "question_pattern": "建议您在医生给您下一步训练建议前，暂停训练。?",
      "reply_templates": [
        {
          "id": "d5bb980a",
          "user_reply_pattern": "^(好的|好|知道了|明白了|收到|了解|遵医嘱|听医生的|行|可以|没问题)",
          "next_question": "您使用的辅助仪器都还正常吧？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "training_pause",
        "agreed"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.418000"
    },
    {
      "id": "9d77c1d3",
      "question_pattern": "您使用的辅助仪器都还正常吧[？?]?",
      "reply_templates": [
        {
          "id": "150f89aa",
          "user_reply_pattern": ".*(正常|好的|挺好|还好|没问题|很稳|稳固|牢固|挺紧|很紧|绑得紧|固定得好|都挺好).*",
          "next_question": "您进行锻炼已经多久了？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "equipment_status",
        "normal"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.419000"
    },
    {
      "id": "ade6a1a5",
      "question_pattern": "您进行锻炼已经多久了[？?]?",
      "reply_templates": [
        {
          "id": "b6778c5c",
          "user_reply_pattern": ".*?(?:锻炼|练|做|进行|开始|坚持|有|差不多|大约|约|快|将近|已经|差点|接近|快到|差不多到|从|自从)?.*?(\\d+(?:\\.\\d+)?)\\s*(个月|月份|月|周|星期|礼拜|天|日|年)",
          "next_question": "很好，{duration}{unit}的锻炼时间很不错，请您在身体没有不适感情况下坚持锻炼。",
          "variables": {
            "duration": "$1",
            "unit": "$2"
          },
          "hit_count": 0
        },
        {
          "id": "d3ed7dba",
          "user_reply_pattern": ".*(?:差不多|快到|将近|快|已经)?.*(一个半|两个半|三个半|半个)月",
          "next_question": "很好，您的锻炼时间很不错，请您在身体没有不适感情况下坚持锻炼。",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "total_exercise_duration"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.419000"
    },
    {
      "id": "52afcad0",
      "question_pattern": "您进行锻炼已经多久了[？?]?",
      "reply_templates": [
        {
          "id": "8152bc1d",
          "user_reply_pattern": ".*(?:差不多|快到|将近|快|已经)?.*(一个半|两个半|三个半|半个)月",
          "next_question": "很好，您的锻炼时间很不错，请您在身体没有不适感情况下坚持锻炼。",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "total_exercise_duration",
        "chinese_expression"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.419000"
    },
    {
      "id": "519d4333",
      "question_pattern": ".*请您在身体没有不适感情况下坚持锻炼。?",
      "reply_templates": [
        {
          "id": "ab77d056",
          "user_reply_pattern": ".*(好的|知道了|会的|坚持|继续|感谢|谢谢|一定会|会坚持|会继续).*",
          "next_question": "您做得很好。如果有任何不舒服，可以随时到医院来看，随时来找医生。",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "encouragement",
        "positive_response"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.419000"
    },
    {
      "id": "c2787d28",
      "question_pattern": "如果有任何不舒服，可以随时到医院来看，随时来找医生。?",
      "reply_templates": [
        {
          "id": "7750ad8f",
          "user_reply_pattern": ".*(好的|知道了|明白了|会的|谢谢|感谢|记住了|了解).*",
          "next_question": "还有其他问题需要我帮您解答的吗？",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "hospital_advice",
        "general_response"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.421000"
    },
    {
      "id": "a33b05cd",
      "question_pattern": "还有其他问题需要我帮您解答的吗[？?]?",
      "reply_templates": [
        {
          "id": "5dabe3f4",
          "user_reply_pattern": "^(没有|没有了|没问题|没什么|不用了|就这样|可以了|谢谢|够了)",
          "next_question": "好的，今天就先这样，再见。",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "final_check",
        "no_more_questions"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.421000"
    },
    {
      "id": "fa389222",
      "question_pattern": ".*再见。?",
      "reply_templates": [
        {
          "id": "5e811c5e",
          "user_reply_pattern": ".*(再见|谢谢|拜拜|bye|好的|麻烦了|辛苦了)",
          "next_question": "再见。",
          "variables": {},
          "hit_count": 0
        }
      ],
      "tags": [
        "farewell",
        "standard"
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.422000"
    }
  ],
  "stats": {
    "total_hits": 0,
    "last_updated": "2025-06-06T16:41:32.407000"
  }
}
```

---

## 集合: reply_cache_str

**文档总数**: 1

**分析样本数**: 1

### 字段结构

| 字段名 | 数据类型 | 示例值 |
|-------|---------|--------|
| `_id` | str | `dialogue_cache_str` |
| `question_templates` | list | `[数组，长度: 80]` |

### 示例文档

#### 示例文档 1

```json
{
  "_id": "dialogue_cache_str",
  "question_templates": [
    {
      "id": "6d7e145e",
      "question_pattern": "请问您昨天进行锻炼了吗？",
      "reply_templates": [
        {
          "id": "75ee8b87",
          "user_reply_pattern": "有锻炼",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:41:32.413000"
    },
    {
      "id": "dd9bfbc6",
      "question_pattern": "你在干什么",
      "reply_templates": [
        {
          "id": "a8f7ccbb",
          "user_reply_pattern": "没干什么",
          "next_question": "那就快去玩游戏",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:51:39.650000"
    },
    {
      "id": "f58d0c7b",
      "question_pattern": "您好，我是武汉协和医院骨科的智能助理，请问是张三吗？",
      "reply_templates": [
        {
          "id": "3ac20c2b",
          "user_reply_pattern": "我告诉你。",
          "next_question": "请您明确告知我您是否是张三呢。",
          "hit_count": 0
        },
        {
          "id": "d8089953",
          "user_reply_pattern": "我告诉你",
          "next_question": "请您直接告知我您是否是张三。",
          "hit_count": 0
        },
        {
          "id": "81faf60e",
          "user_reply_pattern": "喂喂喂",
          "next_question": "您好，我在呢，请问您是张三吗？",
          "hit_count": 0
        },
        {
          "id": "9bdb9783",
          "user_reply_pattern": "哎",
          "next_question": "请问您是张三吗？",
          "hit_count": 0
        },
        {
          "id": "714169b0",
          "user_reply_pattern": "对的，对的",
          "next_question": "张三，请问您昨天进行锻炼了吗？",
          "hit_count": 0
        },
        {
          "id": "708847a4",
          "user_reply_pattern": "嗯对的",
          "next_question": "张三，请问您昨天进行锻炼了吗？",
          "hit_count": 0
        },
        {
          "id": "02e3b786",
          "user_reply_pattern": "嗯，是的",
          "next_question": "张三，请问您昨天进行锻炼了吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:53:48.211000"
    },
    {
      "id": "01c7689d",
      "question_pattern": "请您明确告知我您是否是张三呢。",
      "reply_templates": [
        {
          "id": "0df3c524",
          "user_reply_pattern": "我说不能告诉你。",
          "next_question": "这是我们随访流程需要确认的信息哦，请您告知我您是不是张三。 ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:53:48.212000"
    },
    {
      "id": "c133f53c",
      "question_pattern": "这是我们随访流程需要确认的信息哦，请您告知我您是不是张三。 ",
      "reply_templates": [
        {
          "id": "dd218e61",
          "user_reply_pattern": "我好像是的。",
          "next_question": "那请问您昨天进行锻炼了吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:53:48.212000"
    },
    {
      "id": "8ad22316",
      "question_pattern": "请问昨天没有锻炼是什么原因呢",
      "reply_templates": [
        {
          "id": "8c7a5919",
          "user_reply_pattern": "昨天太累了。我昨天不想锻炼。昨天太累了。",
          "next_question": "了解了，那今天您能否继续锻炼呢？",
          "hit_count": 0
        },
        {
          "id": "32c6e045",
          "user_reply_pattern": "昨天比较累",
          "next_question": "那今天能否继续锻炼呢？",
          "hit_count": 0
        },
        {
          "id": "445c7115",
          "user_reply_pattern": "昨天比较累，在家休息",
          "next_question": "那今天能否继续锻炼呢？",
          "hit_count": 0
        },
        {
          "id": "daa483f0",
          "user_reply_pattern": "有点不想锻炼",
          "next_question": "今天能否继续锻炼呢？",
          "hit_count": 0
        },
        {
          "id": "168c3e6f",
          "user_reply_pattern": "天气不好天气不好天气不好天气不好天气不好",
          "next_question": "了解了，那今天能否继续锻炼呢？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:53:48.213000"
    },
    {
      "id": "0404d4d8",
      "question_pattern": "了解了，那今天您能否继续锻炼呢？",
      "reply_templates": [
        {
          "id": "7d4ead97",
          "user_reply_pattern": "睡觉。嗯，今天也不想再练，今天笑想了。睡觉。",
          "next_question": "请您先休息，等过两天再跟您打电话确认。祝您生活愉快，再见。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:53:48.213000"
    },
    {
      "id": "29604b33",
      "question_pattern": "请您直接告知我您是否是张三。",
      "reply_templates": [
        {
          "id": "4f75e070",
          "user_reply_pattern": "我是的",
          "next_question": "张三，请问您昨天进行锻炼了吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T16:58:38.296000"
    },
    {
      "id": "80501165",
      "question_pattern": "请您直接告知我您是否是张三",
      "reply_templates": [
        {
          "id": "05d0f22e",
          "user_reply_pattern": "我是的",
          "next_question": "张三，请问您昨天进行锻炼了吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.393000"
    },
    {
      "id": "a9d2ebc0",
      "question_pattern": "张三，请问您昨天进行锻炼了吗？",
      "reply_templates": [
        {
          "id": "af97dbfd",
          "user_reply_pattern": "哎，我昨天断裂了",
          "next_question": "您是想说昨天锻炼了吗？麻烦您确认一下。",
          "hit_count": 0
        },
        {
          "id": "83ef0615",
          "user_reply_pattern": "看见了",
          "next_question": "我有点不太明白您的意思，您能明确说一下昨天是否进行锻炼了吗？ ",
          "hit_count": 0
        },
        {
          "id": "1829aa91",
          "user_reply_pattern": "有断裂",
          "next_question": "我没太明白您的意思，您能说清楚“有断裂”具体指的是什么情况吗？是锻炼过程中身体某个部位有断裂，还是其他方面呢？",
          "hit_count": 0
        },
        {
          "id": "995fb406",
          "user_reply_pattern": "有",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.394000"
    },
    {
      "id": "bd7e3abb",
      "question_pattern": "您是想说昨天锻炼了吗？麻烦您确认一下。",
      "reply_templates": [
        {
          "id": "34a9e3de",
          "user_reply_pattern": "对的",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.395000"
    },
    {
      "id": "84d580db",
      "question_pattern": "您昨天锻炼了多少下？",
      "reply_templates": [
        {
          "id": "cfe8b955",
          "user_reply_pattern": "哎，我昨天锻炼了756项",
          "next_question": "跟您确认下，您是昨天锻炼了756下，是吗？",
          "hit_count": 0
        },
        {
          "id": "1ff75887",
          "user_reply_pattern": "17654先",
          "next_question": "您是想说锻炼了17654下吗？请您确认一下表述是否准确。",
          "hit_count": 0
        },
        {
          "id": "0e66ff10",
          "user_reply_pattern": "我昨天锻炼了七千六百啊，不对，七千六百五十元",
          "next_question": "您可能表述有误，我问的是锻炼的次数，您再回忆下昨天锻炼了多少下呢。 ",
          "hit_count": 0
        },
        {
          "id": "b7d73321",
          "user_reply_pattern": "四百二十三",
          "next_question": "跟您确认下，您是昨天锻炼了423下，是吗？",
          "hit_count": 0
        },
        {
          "id": "89067fc7",
          "user_reply_pattern": "昨天锻炼了四百家",
          "next_question": "您是想说昨天锻炼了四百下吗？我跟您确认下，您是昨天锻炼了四百下，",
          "hit_count": 0
        },
        {
          "id": "d1f20198",
          "user_reply_pattern": "一百二十五下",
          "next_question": "跟您确认下，您是昨天锻炼了一百二十五下，",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.395000"
    },
    {
      "id": "32103a7a",
      "question_pattern": "很好，您昨天总共花了多长时间完成锻炼",
      "reply_templates": [
        {
          "id": "3cd8205a",
          "user_reply_pattern": "总共花了一个小时啊",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "hit_count": 0
        },
        {
          "id": "b7397bb7",
          "user_reply_pattern": "花了两个小时",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "hit_count": 0
        },
        {
          "id": "bccd8ae9",
          "user_reply_pattern": "我",
          "next_question": "您说。",
          "hit_count": 0
        },
        {
          "id": "01f20f3a",
          "user_reply_pattern": "一个小时",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.396000"
    },
    {
      "id": "45667fa1",
      "question_pattern": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
      "reply_templates": [
        {
          "id": "7a95c341",
          "user_reply_pattern": "我没有什么不符的感觉还挺好呀我没有什么不符的",
          "next_question": "您进行锻炼已经多久了？",
          "hit_count": 0
        },
        {
          "id": "b50f1a75",
          "user_reply_pattern": "没有什么不舒服的感觉还可以呀没有什么不舒服的",
          "next_question": "您使用的辅助仪器都还正常吧？",
          "hit_count": 0
        },
        {
          "id": "158fa4a4",
          "user_reply_pattern": "，还好，没有什么不适的",
          "next_question": "您使用的辅助仪器都还正常吧？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.396000"
    },
    {
      "id": "18e41d8f",
      "question_pattern": "您进行锻炼已经多久了？",
      "reply_templates": [
        {
          "id": "bc9cc481",
          "user_reply_pattern": "有一个小时嗯有一个小时",
          "next_question": "我问的是您从开始锻炼到现在总共过了多久啦，不是这次锻炼花的时间哈。 ",
          "hit_count": 0
        },
        {
          "id": "cb68e578",
          "user_reply_pattern": "有2个星期了",
          "next_question": "很好，那请您在身体没有不适感情况下坚持锻炼。请问您还有其他问题吗？",
          "hit_count": 0
        },
        {
          "id": "6aae1b38",
          "user_reply_pattern": "有一个多星期了",
          "next_question": "很好，那请您在身体没有不适感情况下坚持锻炼。请问您还有其他问题吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.397000"
    },
    {
      "id": "58167240",
      "question_pattern": "我问的是您从开始锻炼到现在总共过了多久啦，不是这次锻炼花的时间哈。 ",
      "reply_templates": [
        {
          "id": "675cc802",
          "user_reply_pattern": "想几个月呢现在一共有一个月了刚开始那现在一共有一个月了想几个月呢",
          "next_question": "很好，那请您在身体没有不适感情况下坚持锻炼。请问您还有其他问题吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.397000"
    },
    {
      "id": "6dff219a",
      "question_pattern": "很好，那请您在身体没有不适感情况下坚持锻炼。请问您还有其他问题吗？",
      "reply_templates": [
        {
          "id": "46c6d34b",
          "user_reply_pattern": "没有问题的",
          "next_question": "感谢您的配合，祝您生活愉快，再见。",
          "hit_count": 0
        },
        {
          "id": "8dab4a08",
          "user_reply_pattern": "我没有",
          "next_question": "感谢您的配合，祝您生活愉快，再见。",
          "hit_count": 0
        },
        {
          "id": "8c360495",
          "user_reply_pattern": "，还有啊",
          "next_question": "请您说一下您的问题。",
          "hit_count": 0
        },
        {
          "id": "d1f4a236",
          "user_reply_pattern": "哎，我没有问题啊，是你在问我",
          "next_question": "好的，如果您之后有任何问题都可以随时联系我们。再见。",
          "hit_count": 0
        },
        {
          "id": "77acac17",
          "user_reply_pattern": "没有了",
          "next_question": "感谢您的配合，祝您早日康复，再见。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:00:32.397000"
    },
    {
      "id": "5678e9cd",
      "question_pattern": "您好，我是武汉协和医院骨科的智能助理，请问是李四吗？",
      "reply_templates": [
        {
          "id": "9260a651",
          "user_reply_pattern": "是的，是的",
          "next_question": "李四，请问您昨天进行锻炼了吗？",
          "hit_count": 0
        },
        {
          "id": "bb5c61c5",
          "user_reply_pattern": "喂",
          "next_question": "请问您是李四吗？",
          "hit_count": 0
        },
        {
          "id": "b0ab7e85",
          "user_reply_pattern": "再见",
          "next_question": "请先不要结束通话哈，我还需要了解您的锻炼情况呢。请问您是李四吗？ ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:03:50.509000"
    },
    {
      "id": "4e44ed50",
      "question_pattern": "您昨天锻炼了多少下",
      "reply_templates": [
        {
          "id": "1264335a",
          "user_reply_pattern": "昨天锻炼了720家",
          "next_question": "跟您确认下，您是昨天锻炼了720下，是吗？",
          "hit_count": 0
        },
        {
          "id": "20094f7e",
          "user_reply_pattern": "我锻炼了四百三十项",
          "next_question": "跟您确认下，您是昨天锻炼了四百三十下，是吗？",
          "hit_count": 0
        },
        {
          "id": "b5515873",
          "user_reply_pattern": "干练了七千零五二点六二下",
          "next_question": "跟您确认下，您是昨天锻炼了七千零五十二点六二下，是吗？",
          "hit_count": 0
        },
        {
          "id": "d3a5393f",
          "user_reply_pattern": "昨天锻炼了四百二十下",
          "next_question": "跟您确认下，您是昨天锻炼了四百二十下，",
          "hit_count": 0
        },
        {
          "id": "638b7dd5",
          "user_reply_pattern": "锻炼了七百五十二项",
          "next_question": "跟您确认下，您是昨天锻炼了七百五十二下，是吗？",
          "hit_count": 0
        },
        {
          "id": "dda9d7ba",
          "user_reply_pattern": "嗯，两百",
          "next_question": "跟您确认下，您是昨天锻炼了两百下，是吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:03:50.510000"
    },
    {
      "id": "f24283d6",
      "question_pattern": "跟您确认下，您是昨天锻炼了720下，是吗？",
      "reply_templates": [
        {
          "id": "5bc7b3ef",
          "user_reply_pattern": "你说什么",
          "next_question": "我是跟您确认一下，您昨天是不是锻炼了720下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:03:50.512000"
    },
    {
      "id": "b5b57473",
      "question_pattern": "我是跟您确认一下，您昨天是不是锻炼了720下？",
      "reply_templates": [
        {
          "id": "f5f68b94",
          "user_reply_pattern": "对对对",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:03:50.512000"
    },
    {
      "id": "1206d905",
      "question_pattern": "很好，您昨天总共花了多长时间完成锻炼？",
      "reply_templates": [
        {
          "id": "553d510b",
          "user_reply_pattern": "好一个响",
          "next_question": "您说。",
          "hit_count": 0
        },
        {
          "id": "ae3127ad",
          "user_reply_pattern": "花了一个小时啊",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "hit_count": 0
        },
        {
          "id": "f67eb618",
          "user_reply_pattern": "是是三百五十家对我搞错了啊，我记错了，昨天是花了三百，锻炼了三百五十项对我搞错了是是三百五十家",
          "next_question": "我明白了，我重新跟您确认下，您昨天锻炼了三百五十下，是吗？",
          "hit_count": 0
        },
        {
          "id": "f008f69c",
          "user_reply_pattern": "两个半小时啊，不对，两个半小时花了一点半一下吧啊，不对，两个半小时两个半小时",
          "next_question": "我确认下，您昨天花了两个半小时完成锻炼，对吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:03:50.513000"
    },
    {
      "id": "b6804808",
      "question_pattern": "您说。",
      "reply_templates": [
        {
          "id": "d335d216",
          "user_reply_pattern": "我说花了一个小时",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "hit_count": 0
        },
        {
          "id": "630f0218",
          "user_reply_pattern": "昨天总共花了20个小时",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:03:50.513000"
    },
    {
      "id": "827ecf1a",
      "question_pattern": "您进行锻炼已经多久了",
      "reply_templates": [
        {
          "id": "3f13db75",
          "user_reply_pattern": "有2个星期了",
          "next_question": "很好，那请您在身体没有不适感情况下坚持锻炼。请问您还有其他问题吗？",
          "hit_count": 0
        },
        {
          "id": "4169180a",
          "user_reply_pattern": "他比较熟哎，不对，他比较比较松他比较熟",
          "next_question": "您是说辅助仪器绑得比较松吗？能说清楚是哪个辅助仪器有这个情况呀。",
          "hit_count": 0
        },
        {
          "id": "c7e77323",
          "user_reply_pattern": "锻炼有一个月了",
          "next_question": "很好，那请您在身体没有不适感情况下坚持锻炼。请问您还有其他问题吗？",
          "hit_count": 0
        },
        {
          "id": "33818677",
          "user_reply_pattern": "四十天回来，四十二四十二天有四十天回来，四十二四十二天",
          "next_question": "我明白了，您是说进行锻炼已经四十二天了，对吗？",
          "hit_count": 0
        },
        {
          "id": "d5a9e9bc",
          "user_reply_pattern": "嗯，他那个松了怎么办呢",
          "next_question": "您先别担心，我会把仪器松动的情况反馈给医生，医生会在12小时内联系您给出解决办法。现在我还是想了解下，",
          "hit_count": 0
        },
        {
          "id": "d75ec39f",
          "user_reply_pattern": "嗯，一周",
          "next_question": "很好，那请您在身体没有不适感情况下坚持锻炼。请问您还有其他问题吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-06T17:03:50.514000"
    },
    {
      "id": "34a720c4",
      "question_pattern": "那今天能否继续锻炼呢？",
      "reply_templates": [
        {
          "id": "3e3d11ca",
          "user_reply_pattern": "我现在不想锻炼",
          "next_question": "请您先休息，等过两天再跟您打电话确认。再见。",
          "hit_count": 0
        },
        {
          "id": "7580d1b3",
          "user_reply_pattern": "因为我看情况吧",
          "next_question": "请问您还有其他问题吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T10:12:13.535000"
    },
    {
      "id": "7dce5b9f",
      "question_pattern": "张三，请问您昨天进行锻炼了吗",
      "reply_templates": [
        {
          "id": "a1b3c369",
          "user_reply_pattern": "昨天断联了",
          "next_question": "不好意思，我没太理解您说的“断联”意思，您是说昨天没有锻炼吗？ ",
          "hit_count": 0
        },
        {
          "id": "3aa0db61",
          "user_reply_pattern": "有的有的",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        },
        {
          "id": "cc09598a",
          "user_reply_pattern": "有锻炼",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        },
        {
          "id": "394559f5",
          "user_reply_pattern": "六幺幺",
          "next_question": "我没太明白您的意思，您是想说昨天锻炼的次数吗？还是请您明确说一下您昨天是否进行锻炼了。 ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.506000"
    },
    {
      "id": "deb6178a",
      "question_pattern": "不好意思，我没太理解您说的“断联”意思，您是说昨天没有锻炼吗？ ",
      "reply_templates": [
        {
          "id": "b33483d1",
          "user_reply_pattern": "我说我昨天有",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.507000"
    },
    {
      "id": "be0e164a",
      "question_pattern": "跟您确认下，您是昨天锻炼了400下，是吗",
      "reply_templates": [
        {
          "id": "cd3326cf",
          "user_reply_pattern": "不对，我是下",
          "next_question": "您是想说昨天锻炼的次数不是400下，能说一下正确的锻炼次数吗？ ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.509000"
    },
    {
      "id": "5a40525c",
      "question_pattern": "您是想说昨天锻炼的次数不是400下，能说一下正确的锻炼次数吗？ ",
      "reply_templates": [
        {
          "id": "8df916d0",
          "user_reply_pattern": "哎，我说昨天锻炼450下",
          "next_question": "跟您确认下，您是昨天锻炼了450下，是吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.511000"
    },
    {
      "id": "ee4f08ab",
      "question_pattern": "您是说辅助仪器绑得比较松吗？能说清楚是哪个辅助仪器有这个情况呀。",
      "reply_templates": [
        {
          "id": "05865c37",
          "user_reply_pattern": "就是你绑腿上的这个呀对就是你绑腿上的这个呀",
          "next_question": "了解了，那您在锻炼过程中，绑腿仪器松动有没有影响到锻炼呢？ ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.514000"
    },
    {
      "id": "87976cba",
      "question_pattern": "了解了，那您在锻炼过程中，绑腿仪器松动有没有影响到锻炼呢？ ",
      "reply_templates": [
        {
          "id": "1129f33c",
          "user_reply_pattern": "影响锻炼倒没有，但是我就感觉它那个技术可能不太准确",
          "next_question": "您是指绑腿仪器记录的锻炼数据可能不准确吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.515000"
    },
    {
      "id": "28f689d7",
      "question_pattern": "您是指绑腿仪器记录的锻炼数据可能不准确吗？",
      "reply_templates": [
        {
          "id": "59e345f2",
          "user_reply_pattern": "对，我也不知道，我就问你们有没有这种可能呢",
          "next_question": "目前不太能确定是否有这种可能。我会把您的情况反馈给医生，医生会在12小时内联系您进一步沟通。鉴于您提到仪器可能数据不准确，建议您在医生给您下一步建议前，暂停使用该仪器锻炼。您还有其他问题吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.518000"
    },
    {
      "id": "236151ae",
      "question_pattern": "目前不太能确定是否有这种可能。我会把您的情况反馈给医生，医生会在12小时内联系您进一步沟通。鉴于您提到仪器可能数据不准确，建议您在医生给您下一步建议前，暂停使用该仪器锻炼。您还有其他问题吗？",
      "reply_templates": [
        {
          "id": "4b7d6cae",
          "user_reply_pattern": "我先绑着看看吧不需要医生，不需要医生那个打电话他人不需要医生，不需要医生那个打电话我先绑着看看吧",
          "next_question": "好的，如果后续您使用过程中还有其他问题或者仪器情况有变化，随时联系我们。请问您还有其他问题吗？ ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.519000"
    },
    {
      "id": "3cb49316",
      "question_pattern": "好的，如果后续您使用过程中还有其他问题或者仪器情况有变化，随时联系我们。请问您还有其他问题吗？ ",
      "reply_templates": [
        {
          "id": "a196230f",
          "user_reply_pattern": "没有没有",
          "next_question": "感谢您的配合，祝您锻炼顺利，再见。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T12:25:42.520000"
    },
    {
      "id": "6eacd59d",
      "question_pattern": "请问您是张三吗？",
      "reply_templates": [
        {
          "id": "e1d087e2",
          "user_reply_pattern": "对对对",
          "next_question": "张三，请问您昨天进行锻炼了吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T17:09:58.298000"
    },
    {
      "id": "9ae60268",
      "question_pattern": "我没太明白您的意思，您能说清楚“有断裂”具体指的是什么情况吗？是锻炼过程中身体某个部位有断裂，还是其他方面呢？",
      "reply_templates": [
        {
          "id": "03160afc",
          "user_reply_pattern": "我说有断",
          "next_question": "您慢慢说，把情况说完整些，这样我才能更好地理解您的意思。 ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T17:20:05.382000"
    },
    {
      "id": "cc7ac04b",
      "question_pattern": "您慢慢说，把情况说完整些，这样我才能更好地理解您的意思。 ",
      "reply_templates": [
        {
          "id": "89ab1f81",
          "user_reply_pattern": "我说有，有断裂",
          "next_question": "您是说身体某个部位在锻炼时有断裂情况吗？请您具体说说是哪个部位。 ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T17:20:05.384000"
    },
    {
      "id": "d3233d68",
      "question_pattern": "您是说身体某个部位在锻炼时有断裂情况吗？请您具体说说是哪个部位。 ",
      "reply_templates": [
        {
          "id": "3934bda7",
          "user_reply_pattern": "不是的，我说我昨天有运动了",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T17:20:05.385000"
    },
    {
      "id": "e8f92349",
      "question_pattern": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适",
      "reply_templates": [
        {
          "id": "0df66e4d",
          "user_reply_pattern": "都还好，就出了点汗",
          "next_question": "您进行锻炼已经多久了？",
          "hit_count": 0
        },
        {
          "id": "31a3070d",
          "user_reply_pattern": "嗯，可以吧，还可以",
          "next_question": "您使用的辅助仪器都还正常吧？",
          "hit_count": 0
        },
        {
          "id": "81c0c295",
          "user_reply_pattern": "嗯，感觉腿比较酸，大腿比较酸",
          "next_question": "这种大腿酸感在锻炼完休息后有没有缓解呢？",
          "hit_count": 0
        },
        {
          "id": "0b0e9fe3",
          "user_reply_pattern": "感觉膝盖有点痛",
          "next_question": "这种膝盖疼痛感在锻炼完休息后有没有缓解呢？",
          "hit_count": 0
        },
        {
          "id": "c40afbec",
          "user_reply_pattern": "嗯，右腿膝关节有一点疼痛",
          "next_question": "这种右腿膝关节的疼痛感在锻炼完休息后有没有缓解呢？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T17:26:49.696000"
    },
    {
      "id": "cbfdb192",
      "question_pattern": "请问您还有其他问题吗？",
      "reply_templates": [
        {
          "id": "a69aac5d",
          "user_reply_pattern": "没有",
          "next_question": "感谢您的配合，祝您早日康复，再见。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T17:32:25.928000"
    },
    {
      "id": "61133aa5",
      "question_pattern": "李四，请问您昨天进行锻炼了吗",
      "reply_templates": [
        {
          "id": "3f6bedde",
          "user_reply_pattern": "昨天断裂了",
          "next_question": "您是想说昨天锻炼中断了吗？请问昨天没有锻炼的原因是什么呢？",
          "hit_count": 0
        },
        {
          "id": "9b2dee0b",
          "user_reply_pattern": "要的要的",
          "next_question": "您的意思是昨天进行锻炼了吗？请明确回复我是或者不是。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T18:24:38.745000"
    },
    {
      "id": "e61ac9d7",
      "question_pattern": "您是想说昨天锻炼中断了吗？请问昨天没有锻炼的原因是什么呢？",
      "reply_templates": [
        {
          "id": "0ebca132",
          "user_reply_pattern": "我说昨天有锻炼",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T18:24:38.747000"
    },
    {
      "id": "fb5d420e",
      "question_pattern": "您是想说锻炼了17654下吗？请您确认一下表述是否准确。",
      "reply_templates": [
        {
          "id": "378df6fd",
          "user_reply_pattern": "对的",
          "next_question": "跟您确认下，您是昨天锻炼了17654下，是吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T18:24:38.751000"
    },
    {
      "id": "0dd29d9d",
      "question_pattern": "很好，您使用的辅助仪器都还正常吧",
      "reply_templates": [
        {
          "id": "2fe0ccc4",
          "user_reply_pattern": "嗯，能不能让医生人工给我打电话",
          "next_question": "我会反馈给医生，医生会在12小时内联系您。请问您还有其他问题吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T18:24:38.755000"
    },
    {
      "id": "e7bb1cf6",
      "question_pattern": "我会反馈给医生，医生会在12小时内联系您。请问您还有其他问题吗？",
      "reply_templates": [
        {
          "id": "32edcf22",
          "user_reply_pattern": "没有",
          "next_question": "感谢您的配合，祝您生活愉快，再见。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-07T18:24:38.756000"
    },
    {
      "id": "71a7577c",
      "question_pattern": "请您说一下您的问题。",
      "reply_templates": [
        {
          "id": "70d8fbeb",
          "user_reply_pattern": "我我好像不记得",
          "next_question": "您慢慢想。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T08:21:29.063000"
    },
    {
      "id": "b2389ae1",
      "question_pattern": "您慢慢想。",
      "reply_templates": [
        {
          "id": "b25de701",
          "user_reply_pattern": "唉如何跟你们人工取得联系啊",
          "next_question": "我会反馈给医生，医生会在12小时内联系您。再见。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T08:21:29.066000"
    },
    {
      "id": "204200c1",
      "question_pattern": "今天能否继续锻炼呢？",
      "reply_templates": [
        {
          "id": "11d1eb97",
          "user_reply_pattern": "说不定去爬山去了嗯，今天我看情况吧，今天说不定去爬山去了",
          "next_question": "好的，希望您能在合适的时候继续坚持锻炼。请问您还有其他问题吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T09:57:18.282000"
    },
    {
      "id": "326c4f32",
      "question_pattern": "好的，请张三本人接一下电话好吗",
      "reply_templates": [
        {
          "id": "672db817",
          "user_reply_pattern": "他怎么不呜的嘞，他怎么去个房子",
          "next_question": "请让张三本人来接电话哈。",
          "hit_count": 0
        },
        {
          "id": "b48aa259",
          "user_reply_pattern": "你打错了，他不不在这里",
          "next_question": "请让张三本人来接电话哈。 ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T09:59:06.852000"
    },
    {
      "id": "ba6a7663",
      "question_pattern": "请让张三本人来接电话哈。",
      "reply_templates": [
        {
          "id": "565c9e39",
          "user_reply_pattern": "我说他怎么不在屋里",
          "next_question": "麻烦您等他回来后让他来接电话哈。 ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T09:59:06.853000"
    },
    {
      "id": "c13c2bde",
      "question_pattern": "您可能表述有误，我问的是锻炼的次数，您再回忆下昨天锻炼了多少下呢。 ",
      "reply_templates": [
        {
          "id": "efe39c03",
          "user_reply_pattern": "啊，不对，六千六百五十箱呃，昨天锻炼七千六百五十下啊，不对，六千六百五十箱",
          "next_question": "您说的“箱”应该是口误啦，我确认下，您是想说昨天锻炼了六千六百五十下，对吗？ ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:14:56.919000"
    },
    {
      "id": "bbb1ed5c",
      "question_pattern": "您说的“箱”应该是口误啦，我确认下，您是想说昨天锻炼了六千六百五十下，对吗？ ",
      "reply_templates": [
        {
          "id": "0574a45b",
          "user_reply_pattern": "啊，对对对",
          "next_question": "跟您确认下，您是昨天锻炼了六千六百五十下，是吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:14:56.920000"
    },
    {
      "id": "f169a021",
      "question_pattern": "跟您确认下，您是昨天锻炼了六千六百五十下，是吗？",
      "reply_templates": [
        {
          "id": "44fe6731",
          "user_reply_pattern": "呃，不对，我记错了昨天是五千六百四十三项",
          "next_question": "您还是口误啦，我想确认的是锻炼次数，您再仔细想想昨天锻炼了多少下呢。 ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:14:56.921000"
    },
    {
      "id": "b79665bf",
      "question_pattern": "您还是口误啦，我想确认的是锻炼次数，您再仔细想想昨天锻炼了多少下呢。 ",
      "reply_templates": [
        {
          "id": "7ba323e4",
          "user_reply_pattern": "我已经说了嘛，昨天是五千七百六十二家",
          "next_question": "您可能有些混淆啦，我问的是锻炼的次数，您重新说一下昨天锻炼了多少下哈。 ",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:14:56.924000"
    },
    {
      "id": "fa76586d",
      "question_pattern": "您可能有些混淆啦，我问的是锻炼的次数，您重新说一下昨天锻炼了多少下哈。 ",
      "reply_templates": [
        {
          "id": "6ea130af",
          "user_reply_pattern": "昨天锻炼了四千三百二十五次",
          "next_question": "跟您确认下，您是昨天锻炼了四千三百二十五次，是吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:14:56.925000"
    },
    {
      "id": "76f1a749",
      "question_pattern": "跟您确认下，您是昨天锻炼了四千三百二十五次，是吗？",
      "reply_templates": [
        {
          "id": "df7cfe67",
          "user_reply_pattern": "对对对",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:14:56.926000"
    },
    {
      "id": "85f0e768",
      "question_pattern": "我明白了，您是说进行锻炼已经四十二天了，对吗？",
      "reply_templates": [
        {
          "id": "f015e0b1",
          "user_reply_pattern": "嗯，是的是的",
          "next_question": "很好，那请您在身体没有不适感情况下坚持锻炼。请问您还有其他问题吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:14:56.929000"
    },
    {
      "id": "c0c94309",
      "question_pattern": "跟您确认下，您是昨天锻炼了四百三十下，是吗？",
      "reply_templates": [
        {
          "id": "e0e2c5b5",
          "user_reply_pattern": "对对对",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:19:35.443000"
    },
    {
      "id": "01224bc0",
      "question_pattern": "跟您确认下，您是昨天锻炼了七千零五十二点六二下，是吗？",
      "reply_templates": [
        {
          "id": "cd0bb73b",
          "user_reply_pattern": "对对对",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:28:07.674000"
    },
    {
      "id": "a903a0c9",
      "question_pattern": "跟您确认下，您是昨天锻炼了四百二十下，",
      "reply_templates": [
        {
          "id": "33c4587c",
          "user_reply_pattern": "对对对",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:32:41.168000"
    },
    {
      "id": "a40eba64",
      "question_pattern": "我没太明白您的意思，您是想说昨天锻炼的次数吗？还是请您明确说一下您昨天是否进行锻炼了。 ",
      "reply_templates": [
        {
          "id": "a6bd5832",
          "user_reply_pattern": "有锻炼",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T10:38:48.361000"
    },
    {
      "id": "b02e598f",
      "question_pattern": "您的意思是昨天进行锻炼了吗？请明确回复我是或者不是。",
      "reply_templates": [
        {
          "id": "16c2c997",
          "user_reply_pattern": "对",
          "next_question": "您昨天锻炼了多少下？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T11:51:02.973000"
    },
    {
      "id": "2911fefa",
      "question_pattern": "您是想说昨天锻炼了四百下吗？我跟您确认下，您是昨天锻炼了四百下，",
      "reply_templates": [
        {
          "id": "3beaacf9",
          "user_reply_pattern": "对对对",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T11:51:02.979000"
    },
    {
      "id": "ba737f43",
      "question_pattern": "我明白了，我重新跟您确认下，您昨天锻炼了三百五十下，是吗？",
      "reply_templates": [
        {
          "id": "083dab1d",
          "user_reply_pattern": "对对对",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T11:51:02.982000"
    },
    {
      "id": "07a889ec",
      "question_pattern": "我确认下，您昨天花了两个半小时完成锻炼，对吗？",
      "reply_templates": [
        {
          "id": "59b3dfc9",
          "user_reply_pattern": "哎，对对",
          "next_question": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T11:51:02.986000"
    },
    {
      "id": "8a148ee2",
      "question_pattern": "您先别担心，我会把仪器松动的情况反馈给医生，医生会在12小时内联系您给出解决办法。现在我还是想了解下，",
      "reply_templates": [
        {
          "id": "572e1060",
          "user_reply_pattern": "啊，好好好",
          "next_question": "您进行锻炼已经多久了？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T11:51:02.989000"
    },
    {
      "id": "cec6222d",
      "question_pattern": "请让张三本人来接电话哈。 ",
      "reply_templates": [
        {
          "id": "e1f17bf3",
          "user_reply_pattern": "我说你打错了这个电话，这里没有张凡",
          "next_question": "麻烦您确认一下是否有患者张三在，让他来接下电话。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T12:41:09.140000"
    },
    {
      "id": "16152328",
      "question_pattern": "麻烦您确认一下是否有患者张三在，让他来接下电话。",
      "reply_templates": [
        {
          "id": "940fc64e",
          "user_reply_pattern": "没有",
          "next_question": "那可能是号码有误了，很抱歉打扰您。再见。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T12:41:09.143000"
    },
    {
      "id": "e0e0f29f",
      "question_pattern": "跟您确认下，您是昨天锻炼了七百五十二下，是吗？",
      "reply_templates": [
        {
          "id": "f10d41ab",
          "user_reply_pattern": "对的",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T15:11:02.676000"
    },
    {
      "id": "cc3f4f3a",
      "question_pattern": "这种不适感是今天才出现的，还是之前就有",
      "reply_templates": [
        {
          "id": "67a812fe",
          "user_reply_pattern": "今天才出现",
          "next_question": "好的，请问休息后酸胀感一直持续没有缓解，是否向我们的医生反应过？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T15:11:02.681000"
    },
    {
      "id": "b7d4941f",
      "question_pattern": "好的，请问休息后酸胀感一直持续没有缓解，是否向我们的医生反应过？",
      "reply_templates": [
        {
          "id": "19f88601",
          "user_reply_pattern": "我才跟你说的呃，没有反馈，你今天打电话问我才跟你说的",
          "next_question": "好的，我会把您的情况反馈给医生，他会在12小时内联络您。 鉴于您本次训练结束后酸胀感一直未缓解，建议您在医生给您下一步训练建议前，暂停训练。\n\n您使用的辅助仪器都还正常吧？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T15:11:02.684000"
    },
    {
      "id": "7f09391d",
      "question_pattern": "跟您确认下，您是昨天锻炼了一百二十五下，",
      "reply_templates": [
        {
          "id": "af3d7c1e",
          "user_reply_pattern": "对的对的",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T15:15:38.299000"
    },
    {
      "id": "623ec30e",
      "question_pattern": "这种膝盖疼痛感在锻炼完休息后有没有缓解呢？",
      "reply_templates": [
        {
          "id": "611120f1",
          "user_reply_pattern": "没有缓解",
          "next_question": "这种膝盖痛是今天才出现的，还是之前就有？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T15:15:38.305000"
    },
    {
      "id": "708d05b7",
      "question_pattern": "这种膝盖痛是今天才出现的，还是之前就有？",
      "reply_templates": [
        {
          "id": "9fdf4dc2",
          "user_reply_pattern": "今天",
          "next_question": "好的，请问休息后膝盖痛一直持续没有缓解，是否向我们的医生反应过？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T15:15:38.308000"
    },
    {
      "id": "83f34219",
      "question_pattern": "好的，请问休息后膝盖痛一直持续没有缓解，是否向我们的医生反应过？",
      "reply_templates": [
        {
          "id": "740acf5f",
          "user_reply_pattern": "没有",
          "next_question": "好的，我会把您的情况反馈给医生，他会在12小时内联络您。 鉴于您本次训练结束后膝盖痛仍未缓解，建议您在医生给您下一步训练建议前，暂停训练。\n\n您使用的辅助仪器都还正常吧？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-08T15:15:38.311000"
    },
    {
      "id": "d73fa48a",
      "question_pattern": "您好，我是武汉协和医院骨科的智能助理，请问是胡吗？",
      "reply_templates": [
        {
          "id": "6a015de8",
          "user_reply_pattern": "对",
          "next_question": "胡，请问您昨天进行锻炼了吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-09T13:10:07.912000"
    },
    {
      "id": "c6c3a4a2",
      "question_pattern": "了解了，那今天能否继续锻炼呢？",
      "reply_templates": [
        {
          "id": "cd6e933d",
          "user_reply_pattern": "天气不好，也不能锻炼",
          "next_question": "请您先休息，等过两天再跟您打电话确认。感谢您的配合，再见。",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-09T13:10:07.919000"
    },
    {
      "id": "f3b26e94",
      "question_pattern": "跟您确认下，您是昨天锻炼了两百下，是吗？",
      "reply_templates": [
        {
          "id": "72ac90f0",
          "user_reply_pattern": "是的",
          "next_question": "很好，您昨天总共花了多长时间完成锻炼？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-09T13:39:51.963000"
    },
    {
      "id": "f995c32a",
      "question_pattern": "这种右腿膝关节的疼痛感在锻炼完休息后有没有缓解呢？",
      "reply_templates": [
        {
          "id": "e6a49899",
          "user_reply_pattern": "有痕迹嗯有痕迹",
          "next_question": "我没太明白您的意思，您是想说疼痛感在休息后有缓解吗？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-09T13:39:51.970000"
    },
    {
      "id": "390e71f4",
      "question_pattern": "我没太明白您的意思，您是想说疼痛感在休息后有缓解吗？",
      "reply_templates": [
        {
          "id": "a876667f",
          "user_reply_pattern": "是的嗯，有环节是的",
          "next_question": "这是正常锻炼情况，不用过于担心。您使用的辅助仪器都还正常吧？",
          "hit_count": 0
        }
      ],
      "enabled": true,
      "created_at": "2025-06-09T13:39:51.973000"
    }
  ]
}
```

---

## 集合: exercise_plans

**文档总数**: 8

**分析样本数**: 5

### 字段结构

| 字段名 | 数据类型 | 示例值 |
|-------|---------|--------|
| `_id` | ObjectId | `ObjectId`, `ObjectId`, `ObjectId` |
| `consecutive_days` | int | `3`, `5`, `15` |
| `created_at` | datetime | `datetime`, `datetime`, `datetime` |
| `created_by` | str | `doctor01`, `doctor01`, `doctor` |
| `current_daily_target` | int | `200`, `200`, `250` |
| `current_week` | int | `1`, `2`, `3` |
| `description` | str | `术后康复训练，逐步增加运动强度，促进关节功能恢复`, `加强型康复训练，适合恢复期患者`, `基础康复训练，已成功完成` |
| `doctor_id` | str | `doctor01`, `doctor01`, `doctor` |
| `doctor_name` | str | `李医生`, `李医生`, `测试医生` |
| `end_date` | str | `2025-07-13`, `2025-07-22`, `2025-06-11` |
| `initial_daily_count` | int | `200`, `150`, `100` |
| `last_modified_by` | str | `doctor01`, `doctor01`, `doctor` |
| `max_rest_days_per_week` | int | `2`, `1`, `2` |
| `overall_completion_rate` | float | `1.0`, `0.93`, `0.95` |
| `patient_id` | str | `P0001`, `P0002`, `P0003` |
| `patient_name` | str | `张三`, `李四`, `王五` |
| `plan_id` | str | `EP_20250621_001`, `EP_20250621_002`, `EP_20250521_001` |
| `plan_name` | str | `膝关节康复训练计划`, `膝关节强化训练计划`, `基础康复训练计划` |
| `start_date` | str | `2025-06-15`, `2025-06-10`, `2025-05-21` |
| `status` | str | `active`, `active`, `completed` |
| `total_actual_count` | int | `1000`, `980`, `3500` |
| `total_rest_days` | int | `2`, `2`, `6` |
| `total_target_count` | int | `1000`, `1050`, `3675` |
| `total_weeks` | int | `4`, `6`, `3` |
| `updated_at` | datetime | `datetime`, `datetime`, `datetime` |
| `weekly_increment` | int | `100`, `50`, `75` |
| `weekly_records` | list | `[数组，长度: 1]`, `[数组，长度: 0]`, `[数组，长度: 0]` |

### 示例文档

#### 示例文档 1

```json
{
  "_id": "6856257ceb33086c336e7cb6",
  "plan_id": "EP_20250621_001",
  "patient_id": "P0001",
  "patient_name": "张三",
  "doctor_id": "doctor01",
  "doctor_name": "李医生",
  "plan_name": "膝关节康复训练计划",
  "description": "术后康复训练，逐步增加运动强度，促进关节功能恢复",
  "status": "active",
  "total_weeks": 4,
  "initial_daily_count": 200,
  "weekly_increment": 100,
  "max_rest_days_per_week": 2,
  "start_date": "2025-06-15",
  "end_date": "2025-07-13",
  "current_week": 1,
  "current_daily_target": 200,
  "weekly_records": [
    {
      "week_number": 1,
      "week_start_date": "2025-06-15",
      "week_end_date": "2025-06-21",
      "target_daily_count": 200,
      "daily_records": [
        {
          "date": "2025-06-15",
          "day_of_week": 1,
          "target_count": 200,
          "actual_count": 180,
          "is_rest_day": false,
          "completion_rate": 0.9,
          "notes": "第一天，适应中"
        },
        {
          "date": "2025-06-16",
          "day_of_week": 2,
          "target_count": 200,
          "actual_count": 200,
          "is_rest_day": false,
          "completion_rate": 1.0,
          "notes": "状态良好"
        },
        {
          "date": "2025-06-17",
          "day_of_week": 3,
          "target_count": 0,
          "actual_count": 0,
          "is_rest_day": true,
          "completion_rate": 1.0,
          "notes": "休息日"
        },
        {
          "date": "2025-06-18",
          "day_of_week": 4,
          "target_count": 200,
          "actual_count": 220,
          "is_rest_day": false,
          "completion_rate": 1.1,
          "notes": "超额完成"
        },
        {
          "date": "2025-06-19",
          "day_of_week": 5,
          "target_count": 200,
          "actual_count": 190,
          "is_rest_day": false,
          "completion_rate": 0.95,
          "notes": "略有疲劳"
        },
        {
          "date": "2025-06-20",
          "day_of_week": 6,
          "target_count": 200,
          "actual_count": 210,
          "is_rest_day": false,
          "completion_rate": 1.05,
          "notes": "恢复良好"
        },
        {
          "date": "2025-06-21",
          "day_of_week": 7,
          "target_count": 0,
          "actual_count": 0,
          "is_rest_day": true,
          "completion_rate": 1.0,
          "notes": "周末休息"
        }
      ],
      "week_completion_rate": 1.0,
      "rest_days_used": 2
    }
  ],
  "total_target_count": 1000,
  "total_actual_count": 1000,
  "overall_completion_rate": 1.0,
  "consecutive_days": 3,
  "total_rest_days": 2,
  "created_at": "2025-06-21T03:22:36.660000",
  "updated_at": "2025-06-21T03:22:36.660000",
  "created_by": "doctor01",
  "last_modified_by": "doctor01"
}
```

#### 示例文档 2

```json
{
  "_id": "6856257ceb33086c336e7cb7",
  "plan_id": "EP_20250621_002",
  "patient_id": "P0002",
  "patient_name": "李四",
  "doctor_id": "doctor01",
  "doctor_name": "李医生",
  "plan_name": "膝关节强化训练计划",
  "description": "加强型康复训练，适合恢复期患者",
  "status": "active",
  "total_weeks": 6,
  "initial_daily_count": 150,
  "weekly_increment": 50,
  "max_rest_days_per_week": 1,
  "start_date": "2025-06-10",
  "end_date": "2025-07-22",
  "current_week": 2,
  "current_daily_target": 200,
  "weekly_records": [],
  "total_target_count": 1050,
  "total_actual_count": 980,
  "overall_completion_rate": 0.93,
  "consecutive_days": 5,
  "total_rest_days": 2,
  "created_at": "2025-06-10T03:22:36.660000",
  "updated_at": "2025-06-21T03:22:36.660000",
  "created_by": "doctor01",
  "last_modified_by": "doctor01"
}
```

---

## 集合: sys_info

**文档总数**: 7

**分析样本数**: 5

### 字段结构

| 字段名 | 数据类型 | 示例值 |
|-------|---------|--------|
| `_id` | ObjectId | `ObjectId`, `ObjectId`, `ObjectId` |
| `batch_interval` | int | `30` |
| `batch_size` | int | `10` |
| `call_interval` | int | `2` |
| `call_time_slots` | list | `[数组，长度: 3]` |
| `created_at` | datetime | `datetime`, `datetime`, `datetime` |
| `doc_type` | str | `user_account`, `user_account`, `user_account` |
| `email` | str | `<EMAIL>`, `<EMAIL>`, `<EMAIL>` |
| `full_name` | str | `李医生`, `系统管理员`, `张俊驰` |
| `is_active` | bool | `True`, `True`, `True` |
| `last_call_time` | NoneType | `null` |
| `last_login` | datetime | `datetime`, `datetime`, `datetime` |
| `last_status_update` | datetime | `datetime` |
| `login_count` | int | `23`, `52`, `1` |
| `max_calls_per_day` | NoneType | `null` |
| `max_concurrent_calls` | int | `3` |
| `password_hash` | str | `$2b$12$UEcKGwrsIOb6dV9SwC05peoVLSTq1INkOEPx1ErZ4VLRKsuWb5obC`, `$2b$12$i5ABv0K4bT6WCV7tTpbiXeE2IHLJkIld/46wqJKZSwW2OfEHI7H.u`, `$2b$12$c2MOO8Q4/Ew3YNVYG6UisOcfTFXR4smjB9BAexbxVXP1W3R.hxLQC` |
| `phone` | NoneType | str | `***********`, `null`, `***********` |
| `retry_interval` | int | `5` |
| `retry_times` | int | `3` |
| `role` | str | `医生`, `管理员`, `医生` |
| `running_duration` | int | `0` |
| `skip_weekends` | bool | `False` |
| `start_time` | datetime | `datetime` |
| `status` | str | `运行中` |
| `system_errors` | list | `[数组，长度: 0]` |
| `system_name` | str | `医疗随访AI电话系统` |
| `total_calls_all` | int | `0` |
| `total_calls_today` | int | `0` |
| `updated_at` | datetime | `datetime`, `datetime`, `datetime` |
| `username` | str | `doctor01`, `admin`, `zjcdoc` |
| `version` | str | `1.0.0` |

### 示例文档

#### 示例文档 1

```json
{
  "_id": "684395d54a15668dd8f8714d",
  "doc_type": "user_account",
  "username": "doctor01",
  "password_hash": "$2b$12$UEcKGwrsIOb6dV9SwC05peoVLSTq1INkOEPx1ErZ4VLRKsuWb5obC",
  "role": "医生",
  "full_name": "李医生",
  "email": "<EMAIL>",
  "phone": "***********",
  "is_active": true,
  "last_login": "2025-06-21T04:41:33.254000",
  "login_count": 23,
  "created_at": "2025-06-07T09:28:53.779000",
  "updated_at": "2025-06-07T03:15:57.295000"
}
```

#### 示例文档 2

```json
{
  "_id": "684395d54a15668dd8f8714c",
  "doc_type": "user_account",
  "username": "admin",
  "password_hash": "$2b$12$i5ABv0K4bT6WCV7tTpbiXeE2IHLJkIld/46wqJKZSwW2OfEHI7H.u",
  "role": "管理员",
  "full_name": "系统管理员",
  "email": "<EMAIL>",
  "phone": null,
  "is_active": true,
  "last_login": "2025-06-21T04:31:40.989000",
  "login_count": 52,
  "created_at": "2025-06-07T09:28:53.595000",
  "updated_at": "2025-06-07T02:36:39.199000"
}
```

---

## 集合: personnel

**文档总数**: 10

**分析样本数**: 5

### 字段结构

| 字段名 | 数据类型 | 示例值 |
|-------|---------|--------|
| `_id` | ObjectId | `ObjectId`, `ObjectId`, `ObjectId` |
| `age` | int | `35`, `38`, `45` |
| `assigned_doctor` | str | `doctor01`, `zjcdoc`, `doctor` |
| `assigned_doctor_name` | str | `李医生`, `张俊驰`, `测试医生` |
| `assignment_date` | str | `2025-06-19`, `2025-06-19`, `2025-06-19` |
| `created_at` | datetime | `datetime`, `datetime`, `datetime` |
| `enrollment_date` | str | `2024-01-15`, `2024-01-10`, `2025-06-09` |
| `gender` | str | `男`, `女`, `男` |
| `name` | str | `张三`, `李四`, `胡` |
| `patient_id` | str | `P0001`, `P0002`, `P0003` |
| `phone` | str | `***********`, `17764016802`, `13808625556` |
| `training_status` | str | `训练中`, `训练中`, `终止` |
| `updated_at` | datetime | `datetime`, `datetime`, `datetime` |

### 示例文档

#### 示例文档 1

```json
{
  "_id": "683d2b16fa75c786fa6d7344",
  "name": "张三",
  "phone": "***********",
  "age": 35,
  "gender": "男",
  "enrollment_date": "2024-01-15",
  "training_status": "训练中",
  "created_at": "2025-06-02T12:39:50.702000",
  "updated_at": "2025-06-19T12:25:35.370000",
  "assigned_doctor": "doctor01",
  "assigned_doctor_name": "李医生",
  "assignment_date": "2025-06-19",
  "patient_id": "P0001"
}
```

#### 示例文档 2

```json
{
  "_id": "683d2b16fa75c786fa6d7345",
  "name": "李四",
  "phone": "17764016802",
  "age": 38,
  "gender": "女",
  "enrollment_date": "2024-01-10",
  "training_status": "训练中",
  "created_at": "2025-06-02T12:39:50.726000",
  "updated_at": "2025-06-19T12:25:35.371000",
  "assigned_doctor": "zjcdoc",
  "assigned_doctor_name": "张俊驰",
  "assignment_date": "2025-06-19",
  "patient_id": "P0002"
}
```

---

