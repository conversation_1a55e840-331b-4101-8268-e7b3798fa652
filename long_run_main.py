"""
长时间运行的AI电话机器人
支持在每天的多个时间段按照数据库名单并发拨打电话
"""

import asyncio
import datetime
from typing import Dict, Set, Optional

# 从独立的服务模块导入需要的函数和配置
from call_service import get_personnel_list, concurrent_calls
from med_config import SCHEDULE_CONFIG, LOG_CONFIG
from log_utils import info, warning, error, debug
from db_utils import db_client


class CallScheduler:
    """电话调度器 - 管理每天的拨号时间段"""
    
    def __init__(self):
        self.config = SCHEDULE_CONFIG
        self.executed_slots: Dict[str, Set[str]] = {}  # 记录每天已执行的时间段
        self.is_running = False
        
    def get_current_time(self) -> datetime.datetime:
        """获取当前本地时间"""
        return datetime.datetime.now()
    
    def get_date_key(self, dt: datetime.datetime = None) -> str:
        """获取日期键（用于记录执行状态）"""
        if dt is None:
            dt = self.get_current_time()
        return dt.strftime("%Y-%m-%d")
    
    def is_weekend(self, dt: datetime.datetime = None) -> bool:
        """检查是否为周末"""
        if dt is None:
            dt = self.get_current_time()
        return dt.weekday() >= 5  # 5=Saturday, 6=Sunday
    
    def parse_time(self, time_str: str) -> datetime.time:
        """解析时间字符串"""
        return datetime.time.fromisoformat(time_str)
    
    def is_in_time_slot(self, slot: Dict, current_time: datetime.datetime = None) -> bool:
        """检查当前时间是否在指定时间段内"""
        if current_time is None:
            current_time = self.get_current_time()
            
        start_time = self.parse_time(slot["start_time"])
        end_time = self.parse_time(slot["end_time"])
        current_time_only = current_time.time()
        
        return start_time <= current_time_only <= end_time
    
    def get_active_time_slot(self) -> Optional[Dict]:
        """获取当前活跃的时间段"""
        current_time = self.get_current_time()
        
        # 检查是否跳过周末
        if self.config.get("skip_weekends", False) and self.is_weekend(current_time):
            return None
            
        # 查找当前时间在哪个时间段内
        for slot in self.config["time_slots"]:
            if self.is_in_time_slot(slot, current_time):
                return slot
                
        return None
    
    def is_slot_executed_today(self, slot_name: str) -> bool:
        """检查今天是否已经执行过某个时间段"""
        date_key = self.get_date_key()
        if date_key not in self.executed_slots:
            self.executed_slots[date_key] = set()
        return slot_name in self.executed_slots[date_key]
    
    def mark_slot_executed(self, slot_name: str):
        """标记时间段为已执行"""
        date_key = self.get_date_key()
        if date_key not in self.executed_slots:
            self.executed_slots[date_key] = set()
        self.executed_slots[date_key].add(slot_name)
        
        # 清理旧的执行记录（保留最近7天）
        self.cleanup_old_records()
    
    def cleanup_old_records(self):
        """清理旧的执行记录"""
        current_date = self.get_current_time().date()
        cutoff_date = current_date - datetime.timedelta(days=7)
        
        dates_to_remove = []
        for date_str in self.executed_slots.keys():
            date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
            if date_obj < cutoff_date:
                dates_to_remove.append(date_str)
        
        for date_str in dates_to_remove:
            del self.executed_slots[date_str]
    
    def should_execute_calls(self) -> tuple[bool, Optional[Dict]]:
        """判断是否应该执行拨号"""
        # 检查调度是否启用
        if not self.config.get("enabled", True):
            return False, None
            
        # 获取当前活跃的时间段
        active_slot = self.get_active_time_slot()
        if not active_slot:
            return False, None
            
        # 检查是否已经执行过
        if self.is_slot_executed_today(active_slot["name"]):
            return False, active_slot
            
        return True, active_slot
    
    async def execute_calls(self, slot: Dict):
        """执行拨号任务"""
        slot_name = slot["name"]
        info("🎯 开始执行 %s (%s - %s) 的拨号任务", 
             slot_name, slot["start_time"], slot["end_time"])
        
        try:
            # 获取人员名单，只获取训练中的患者
            personnel_list = await get_personnel_list(filter_training_status="训练中")
            
            if not personnel_list:
                warning("⚠️  %s: 未获取到训练中的人员名单，跳过此次拨号", slot_name)
                return
            
            info("📞 %s: 准备拨打 %d 个电话", slot_name, len(personnel_list))
            
            # 执行并发拨号
            await concurrent_calls(personnel_list)
            
            # 标记为已执行
            self.mark_slot_executed(slot_name)
            
            info("✅ %s: 拨号任务完成", slot_name)
            
        except Exception as e:
            error("❌ %s: 拨号任务执行失败: %s", slot_name, e)
    
    def get_next_time_slot_info(self) -> str:
        """获取下一个时间段的信息"""
        current_time = self.get_current_time()
        current_date = current_time.date()
        
        # 查找今天剩余的时间段
        for slot in self.config["time_slots"]:
            start_time = self.parse_time(slot["start_time"])
            slot_datetime = datetime.datetime.combine(current_date, start_time)
            
            if slot_datetime > current_time and not self.is_slot_executed_today(slot["name"]):
                time_diff = slot_datetime - current_time
                hours, remainder = divmod(time_diff.total_seconds(), 3600)
                minutes = remainder // 60
                return f"下一个时间段: {slot['name']} ({slot['start_time']}) - 还有 {int(hours)}小时{int(minutes)}分钟"
        
        # 如果今天没有剩余时间段，显示明天第一个时间段
        tomorrow = current_date + datetime.timedelta(days=1)
        if self.config["time_slots"]:
            first_slot = self.config["time_slots"][0]
            first_time = self.parse_time(first_slot["start_time"])
            first_slot_datetime = datetime.datetime.combine(tomorrow, first_time)
            time_diff = first_slot_datetime - current_time
            hours, remainder = divmod(time_diff.total_seconds(), 3600)
            minutes = remainder // 60
            return f"下一个时间段: 明天 {first_slot['name']} ({first_slot['start_time']}) - 还有 {int(hours)}小时{int(minutes)}分钟"
        
        return "无下一个时间段"
    
    async def run(self):
        """运行调度器"""
        self.is_running = True
        check_interval = self.config.get("check_interval", 60)
        
        info("🚀 AI电话机器人调度器启动")
        info("⏰ 检查间隔: %d 秒", check_interval)
        info("🕐 时间段配置:")
        for slot in self.config["time_slots"]:
            info("   %s: %s - %s", slot["name"], slot["start_time"], slot["end_time"])
        
        while self.is_running:
            try:
                current_time = self.get_current_time()
                should_execute, active_slot = self.should_execute_calls()
                
                if should_execute and active_slot:
                    await self.execute_calls(active_slot)
                else:
                    # 显示状态信息
                    if active_slot and self.is_slot_executed_today(active_slot["name"]):
                        debug("⏸️  当前时间段 %s 今天已执行，等待下一个时间段", active_slot["name"])
                    else:
                        next_info = self.get_next_time_slot_info()
                        debug("💤 当前不在拨号时间段，%s", next_info)
                
                # 等待检查间隔
                await asyncio.sleep(check_interval)
                
            except KeyboardInterrupt:
                info("⏹️  收到中断信号，正在停止调度器...")
                break
            except Exception as e:
                error("❌ 调度器运行出错: %s", e)
                await asyncio.sleep(check_interval)
        
        self.is_running = False
        info("🔚 调度器已停止")
    
    def stop(self):
        """停止调度器"""
        self.is_running = False


def init_logging():
    """初始化日志系统"""
    # 根据配置设置日志级别
    from log_utils import LogLevel, logger
    
    level_map = {
        "DEBUG": LogLevel.DEBUG,
        "INFO": LogLevel.INFO,
        "WARNING": LogLevel.WARNING,
        "ERROR": LogLevel.ERROR,
        "CRITICAL": LogLevel.CRITICAL
    }
    
    log_level = level_map.get(LOG_CONFIG.get("level", "INFO"), LogLevel.INFO)
    logger.set_level(log_level)
    
    # 设置其他日志选项
    logger.use_colors = LOG_CONFIG.get("use_colors", True)
    logger.use_icons = LOG_CONFIG.get("use_icons", True)
    logger.show_timestamp = LOG_CONFIG.get("show_timestamp", True)
    logger.show_name = LOG_CONFIG.get("show_name", True)


async def main():
    """长时间运行的主函数"""
    # 初始化日志系统
    init_logging()
    
    info("=== AI电话机器人 - 长时间运行模式 ===")
    
    # 检查调度配置
    if not SCHEDULE_CONFIG.get("enabled", True):
        error("调度功能未启用，请在配置文件中设置 SCHEDULE_CONFIG['enabled'] = True")
        return
    
    if not SCHEDULE_CONFIG.get("time_slots"):
        error("未配置时间段，请在配置文件中设置 SCHEDULE_CONFIG['time_slots']")
        return
    
    scheduler = CallScheduler()
    
    try:
        await scheduler.run()
    except KeyboardInterrupt:
        info("⏹️  程序被用户中断")
    except Exception as e:
        error("❌ 程序运行出错: %s", e)
    finally:
        # 关闭数据库连接
        await db_client.close()
        info("🔚 程序已退出")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        warning("⏹️  程序被用户中断")
    except Exception as e:
        error("❌ 程序启动失败: %s", e)
