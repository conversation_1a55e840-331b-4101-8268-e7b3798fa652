#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试邮件患者编号修复
"""

import asyncio
from datetime import datetime
from tools.patient_email_sender import send_patient_report_email_async
from log_utils import info


async def test_patient_id_fix():
    """测试患者编号修复"""
    info("=== 测试患者编号修复 ===")
    
    # 模拟通话记录数据（没有患者编号字段）
    patient_data_without_id = {
        "患者名字": "张三",
        # 注意：这里故意不包含"患者编号"字段，模拟实际通话记录的情况
        "手机号": "18971492577",
        "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "记录日期": datetime.now().strftime("%Y-%m-%d"),
        "拨号状态": "通话结束",
        "训练完成情况": "未完成",
        "训练次数": "50次",
        "训练时长": "8分钟",
        "是否有不适感": "是",
        "不适感内容": "膝盖疼痛严重",
        "不适感锻炼结束后是否恢复": "否",
        "是否需要医生人工和患者联系": "是",
        "锻炼辅助仪器是否有问题": "否",
        "锻炼辅助仪器问题内容": "正常"
    }
    
    info("原始数据中的患者编号字段:")
    info(f"  患者编号: {patient_data_without_id.get('患者编号', '【不存在】')}")
    
    try:
        info("正在发送邮件...")
        result = await send_patient_report_email_async(patient_data_without_id)
        
        if result["success"]:
            info("✅ 邮件发送成功!")
            details = result.get("details", {})
            info(f"  邮件主题: {details.get('subject')}")
            info(f"  患者编号: {details.get('patient_id')}")
            info(f"  医生邮箱: {details.get('doctor_email')}")
            
            # 检查邮件主题中是否包含正确的患者编号
            subject = details.get('subject', '')
            if 'P0001' in subject:
                info("✅ 邮件主题中包含正确的患者编号")
            else:
                info(f"❌ 邮件主题中患者编号不正确: {subject}")
                
        else:
            info(f"❌ 邮件发送失败: {result['error']}")
            if result.get("details"):
                info(f"详细信息: {result['details']}")
                
    except Exception as e:
        info(f"测试过程中出错: {e}")


async def test_with_existing_patient_id():
    """测试已有患者编号的情况"""
    info("=== 测试已有患者编号的情况 ===")
    
    # 模拟已经包含患者编号的数据
    patient_data_with_id = {
        "患者名字": "李四",
        "患者编号": "P0002",  # 已经包含患者编号
        "手机号": "18971492577",
        "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "是否有不适感": "是",
        "不适感内容": "腰部疼痛",
        "是否需要医生人工和患者联系": "是"
    }
    
    info("原始数据中的患者编号字段:")
    info(f"  患者编号: {patient_data_with_id.get('患者编号', '【不存在】')}")
    
    try:
        result = await send_patient_report_email_async(patient_data_with_id)
        
        if result["success"]:
            info("✅ 邮件发送成功!")
            details = result.get("details", {})
            subject = details.get('subject', '')
            info(f"  邮件主题: {subject}")
            
            # 检查是否使用了原有的患者编号
            if 'P0002' in subject:
                info("✅ 使用了原有的患者编号")
            else:
                info(f"❌ 患者编号不正确: {subject}")
                
        else:
            info(f"❌ 邮件发送失败: {result['error']}")
            
    except Exception as e:
        info(f"测试过程中出错: {e}")


async def main():
    """主测试函数"""
    try:
        await test_patient_id_fix()
        print("\n" + "="*60 + "\n")
        await test_with_existing_patient_id()
        
    except Exception as e:
        info(f"测试过程中出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
