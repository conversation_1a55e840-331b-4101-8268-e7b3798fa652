# 简单AI电话机器人

这是一个基于Python实现的AI电话机器人，支持主动拨打电话、连续语音识别和TTS回复功能。
底层是Freeswitch实现通话功能，利用NATS消息队列和底层Freeswitch通信

## 功能特性

- ✅ 主动拨打电话
- ✅ 连续语音识别（不需要反复启动识别）
- ✅ TTS语音回复（简单重复用户说的话）
- ✅ 模块化设计，代码结构清晰
- ✅ 完整的通话状态管理
- ✅ 优雅的资源清理和错误处理

## 项目结构

```
SuiFangMedCall/
├── .idea/              # IDE配置文件 (例如 PyCharm)
├── api_demo/           # API使用示例或演示代码
├── llm/                # 大语言模型 (LLM) 相关代码
├── __pycache__/        # Python编译的字节码文件缓存
├── config.py           # 配置文件
├── rpc_utils.py        # RPC请求工具类
├── speech_handler.py   # 语音处理器
├── call_manager.py     # 通话管理器
├── utils.py            # 通用工具函数
├── main.py             # 主程序入口
├── llm_client.py       # LLM客户端，用于与大语言模型交互
├── requirements.txt    # 依赖包列表
└── README.md           # 说明文档
```

## 核心模块说明

### 1. config.py
包含所有配置信息：
- NATS服务器连接配置
- 电话拨号配置
- TTS引擎配置
- ASR连续识别配置

### 2. rpc_utils.py
封装所有RPC请求：
- 拨打电话 (`dial`)
- 应答电话 (`answer`)
- 启动连续语音识别 (`start_continuous_asr`)
- 播放TTS (`play_tts`)
- 停止识别 (`stop_detect_speech`)
- 挂断电话 (`hangup`)

### 3. speech_handler.py
处理语音交互：
- 处理连续语音识别事件
- 识别结果的解析和处理
- TTS回复生成（简单重复用户话语）
- 特殊指令处理（结束通话等）

### 4. call_manager.py
管理通话流程：
- 通话状态管理
- 处理通话事件（振铃、接通、挂断等）
- 协调语音处理流程
- 资源清理

### 5. main.py
主程序入口：
- 初始化所有模块
- 用户交互界面
- 程序生命周期管理

### 6. utils.py (新增)
包含项目中使用到的通用工具函数或类。

### 7. llm_client.py (新增)
负责与大语言模型 (LLM) 服务进行交互，例如发送请求、处理响应等。

### 8. db_utils.py (数据库工具)
MongoDB数据库操作工具类，提供完整的CRUD功能：
- 人员名单管理（患者基本信息和训练状态）
- 通话记录管理（详细通话内容和训练情况）
- 数据统计和分析功能

### 9. db_example.py (数据库使用示例)
数据库操作的完整使用示例，展示如何进行各种CRUD操作。

### 10. DATABASE_SCHEMA.md (数据库架构文档)
详细的数据库设计文档，包含集合结构、字段说明、索引建议等。

### 11. api_demo/ (新增目录)
存放 API 使用示例代码，方便开发者理解和测试各项 API 功能。

### 12. llm/ (新增目录)
存放与大语言模型集成相关的具体实现代码，例如提示工程、结果解析等。

## 安装和使用

### 1. 安装依赖
```bash
cd SuiFangMedCall
pip install -r requirements.txt
```

### 2. 数据库配置
确保MongoDB服务已启动，并根据需要修改 `config.py` 中的数据库配置：
```python
MONGODB_CONFIG = {
    "connection_string": "mongodb://localhost:27017",
    "db_name": "med_call_records"
}
```

### 3. 配置修改
编辑 `config.py` 文件，根据你的环境修改配置：
- NATS服务器地址
- 拨号字符串模板
- TTS和ASR引擎设置

### 4. 数据库测试
```bash
# 测试数据库连接和功能
python db_example.py
```

### 5. 运行程序
```bash
python main.py
```

### 6. 使用流程
1. 程序启动后会提示输入电话号码
2. 输入完整的手机号码（如：18971492577）
3. 程序会自动拨打电话并等待接通
4. 电话接通后会播放欢迎语
5. 启动连续语音识别，开始对话
6. AI会简单重复用户说的话
7. 说"再见"、"结束"等词语可以结束通话

## 工作原理

### 连续语音识别
- 使用 `XNode.DetectSpeech` API，不传 `media` 参数启动后台识别
- 通过 `Event.DetectedData` 事件获取识别结果
- 支持中间结果和最终结果
- 可以在识别过程中播放TTS

### 通话流程
1. **拨号阶段**：发送 `XNode.Dial` 请求
2. **等待接通**：监听 `Event.Channel` 事件
3. **接通处理**：播放欢迎语，启动语音识别
4. **对话阶段**：持续监听语音，生成TTS回复
5. **结束通话**：清理资源，挂断电话

## 注意事项

1. **网络连接**：确保能够连接到NATS服务器
2. **话务权限**：确保有拨打电话的权限和余额
3. **语音引擎**：确保TTS和ASR引擎配置正确
4. **电话号码**：输入正确的手机号码格式

## 扩展建议

1. **智能回复**：集成LLM API，实现智能对话
2. **语音情感**：添加情感识别和情感化TTS
3. **多轮对话**：添加对话状态管理和上下文记忆
4. **数据记录**：添加通话记录和数据分析
5. **并发支持**：支持同时处理多个通话

## 故障排除

1. **连接失败**：检查NATS服务器地址和网络连接
2. **拨号失败**：检查拨号字符串格式和话务权限
3. **识别无响应**：检查ASR引擎配置和网络状况
4. **TTS播放失败**：检查TTS引擎配置和文本内容 

整个项目结构：
graph TD
    A["用户界面<br/>main.py"] --> B["通话管理器<br/>call_manager.py"]
    A --> C["配置文件<br/>config.py"]
    
    B --> D["语音处理器<br/>speech_handler.py"]
    B --> E["RPC工具<br/>rpc_utils.py"]
    B --> F["LLM客户端<br/>llm_client.py"]
    B --> G["数据库工具<br/>db_utils.py"]
    
    E --> H["NATS消息队列"]
    H --> I["Freeswitch<br/>电话系统"]
    
    F --> J["豆包大模型<br/>LLM服务"]
    
    G --> K["MongoDB数据库"]
    K --> L["人员名单集合<br/>personnel"]
    K --> M["通话记录集合<br/>call_records"]
    
    D --> N["连续语音识别<br/>ASR"]
    D --> O["语音合成<br/>TTS"]
    
    P["数据库示例<br/>db_example.py"] --> G
    Q["工具函数<br/>utils.py"] --> B
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style G fill:#e8f5e8
    style K fill:#fff3e0
    style J fill:#fce4ec