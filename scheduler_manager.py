"""
调度器管理器
使用多进程方式管理长时间运行的AI电话机器人系统
提供启动、停止、重启等API接口
"""

import asyncio
import multiprocessing
import time
import signal
import os
from typing import Optional
from log_utils import info, error, debug, warning
from call_service import get_personnel_list, concurrent_calls
from med_config import SCHEDULE_CONFIG, LOG_CONFIG
# 导入异步的拨号服务函数
from call_service import make_immediate_call as async_make_immediate_call

class SchedulerProcess(multiprocessing.Process):
    """调度器进程"""
    
    def __init__(self, process_queue):
        super().__init__()
        self.process_queue = process_queue
        self.daemon = False  # 不设置为守护进程，便于控制
        
    def run(self):
        """进程主函数"""
        try:
            # 初始化日志系统（子进程需要重新初始化）
            from long_run_main import init_logging
            init_logging()
            
            info("调度器进程 %d 启动", os.getpid())
            
            # 运行调度器
            asyncio.run(self._run_scheduler())
            
        except KeyboardInterrupt:
            info("调度器进程收到中断信号")
        except Exception as e:
            error("调度器进程运行出错: %s", e)
        finally:
            info("调度器进程 %d 结束", os.getpid())
    
    async def _run_scheduler(self):
        """运行调度器的异步函数"""
        from long_run_main import CallScheduler
        
        scheduler = CallScheduler()
        
        # 修改调度器，使其能响应进程队列的命令
        check_interval = scheduler.config.get("check_interval", 60)
        
        info("🚀 AI电话机器人调度器启动")
        info("⏰ 检查间隔: %d 秒", check_interval)
        info("🕐 时间段配置:")
        for slot in scheduler.config["time_slots"]:
            info("   %s: %s - %s", slot["name"], slot["start_time"], slot["end_time"])
        
        scheduler.is_running = True
        
        while scheduler.is_running:
            try:
                # 检查是否有来自主进程的命令
                try:
                    if not self.process_queue.empty():
                        command = self.process_queue.get_nowait()
                        if command == "stop":
                            info("收到停止命令，正在停止调度器...")
                            break
                        elif command == "refresh":
                            info("收到刷新命令，将在下次执行时刷新人员名单")
                            # 这里可以设置一个标志，在下次拨号时刷新名单
                except:
                    pass  # 队列为空或其他异常，继续正常流程
                
                # 正常的调度逻辑
                should_execute, active_slot = scheduler.should_execute_calls()
                
                if should_execute and active_slot:
                    await scheduler.execute_calls(active_slot)
                else:
                    # 显示状态信息
                    if active_slot and scheduler.is_slot_executed_today(active_slot["name"]):
                        debug("⏸️  当前时间段 %s 今天已执行，等待下一个时间段", active_slot["name"])
                    else:
                        next_info = scheduler.get_next_time_slot_info()
                        debug("💤 当前不在拨号时间段，%s", next_info)
                
                # 等待检查间隔
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                error("❌ 调度器运行出错: %s", e)
                await asyncio.sleep(check_interval)
        
        scheduler.is_running = False
        info("🔚 调度器已停止")


class SchedulerManager:
    """调度器管理器 - API接口类"""
    
    def __init__(self):
        self.process: Optional[SchedulerProcess] = None
        self.process_queue: Optional[multiprocessing.Queue] = None
        self.is_running = False
    
    def start_scheduler(self) -> dict:
        """启动调度器"""
        try:
            if self.is_running:
                return {
                    "success": False,
                    "message": "调度器已经在运行中",
                    "pid": self.process.pid if self.process else None
                }
            
            # 检查调度配置
            if not SCHEDULE_CONFIG.get("enabled", True):
                return {
                    "success": False,
                    "message": "调度功能未启用，请在配置文件中设置 SCHEDULE_CONFIG['enabled'] = True"
                }
            
            if not SCHEDULE_CONFIG.get("time_slots"):
                return {
                    "success": False,
                    "message": "未配置时间段，请在配置文件中设置 SCHEDULE_CONFIG['time_slots']"
                }
            
            # 创建进程间通信队列
            self.process_queue = multiprocessing.Queue()
            
            # 创建并启动调度器进程
            self.process = SchedulerProcess(self.process_queue)
            self.process.start()
            
            self.is_running = True
            
            info("调度器启动成功，进程ID: %d", self.process.pid)
            
            return {
                "success": True,
                "message": f"调度器启动成功，进程ID: {self.process.pid}",
                "pid": self.process.pid
            }
            
        except Exception as e:
            error("启动调度器失败: %s", e)
            return {
                "success": False,
                "message": f"启动调度器失败: {str(e)}"
            }
    
    def stop_scheduler(self) -> dict:
        """停止调度器"""
        try:
            if not self.is_running or not self.process:
                return {
                    "success": False,
                    "message": "调度器未在运行"
                }
            
            # 发送停止命令
            if self.process_queue:
                self.process_queue.put("stop")
            
            # 等待进程结束
            self.process.join(timeout=20)  # 等待最多10秒
            
            if self.process.is_alive():
                # 如果进程还没结束，强制终止
                warning("调度器进程未在规定时间内结束，强制终止")
                self.process.terminate()
                self.process.join(timeout=5)
                
                if self.process.is_alive():
                    # 最后手段
                    warning("强制杀死调度器进程")
                    self.process.kill()
                    self.process.join()
            
            self.is_running = False
            self.process = None
            self.process_queue = None
            
            info("调度器已停止")
            
            return {
                "success": True,
                "message": "调度器已停止"
            }
            
        except Exception as e:
            error("停止调度器失败: %s", e)
            return {
                "success": False,
                "message": f"停止调度器失败: {str(e)}"
            }
    
    def restart_scheduler(self) -> dict:
        """重启调度器"""
        try:
            info("正在重启调度器...")
            
            # 先停止
            stop_result = self.stop_scheduler()
            if not stop_result["success"]:
                return {
                    "success": False,
                    "message": f"重启失败，停止调度器时出错: {stop_result['message']}"
                }
            
            # 等待一下确保进程完全停止
            time.sleep(2)
            
            # 再启动
            start_result = self.start_scheduler()
            if start_result["success"]:
                return {
                    "success": True,
                    "message": f"调度器重启成功，新进程ID: {start_result['pid']}"
                }
            else:
                return {
                    "success": False,
                    "message": f"重启失败，启动调度器时出错: {start_result['message']}"
                }
                
        except Exception as e:
            error("重启调度器失败: %s", e)
            return {
                "success": False,
                "message": f"重启调度器失败: {str(e)}"
            }
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        try:
            if not self.is_running or not self.process:
                return {
                    "success": True,
                    "status": "stopped",
                    "message": "调度器未运行"
                }
            
            if self.process.is_alive():
                return {
                    "success": True,
                    "status": "running",
                    "message": f"调度器正在运行，进程ID: {self.process.pid}",
                    "pid": self.process.pid
                }
            else:
                # 进程已经死了，更新状态
                self.is_running = False
                self.process = None
                return {
                    "success": True,
                    "status": "stopped",
                    "message": "调度器进程已终止"
                }
                
        except Exception as e:
            error("获取调度器状态失败: %s", e)
            return {
                "success": False,
                "message": f"获取调度器状态失败: {str(e)}"
            }
    
    def refresh_personnel(self) -> dict:
        """刷新人员名单"""
        try:
            if self.is_running and self.process_queue:
                # 如果调度器在运行，发送刷新命令
                self.process_queue.put("refresh")
                return {
                    "success": True,
                    "message": "已发送刷新命令到调度器，将在下次拨号时生效"
                }
            else:
                return {
                    "success": True,
                    "message": "调度器未运行，刷新命令将在下次启动时生效"
                }
                
        except Exception as e:
            error("刷新人员名单失败: %s", e)
            return {
                "success": False,
                "message": f"刷新人员名单失败: {str(e)}"
            }
    
    async def make_immediate_call_async(self, phone_number: str, obj_id=None) -> dict:
        """立即拨打电话（异步版本）"""
        try:
            info("收到立即拨号请求: %s", phone_number)
            

            
            # 调用拨号服务
            success = await async_make_immediate_call(phone_number, obj_id)
            
            if success:
                return {
                    "success": True,
                    "message": f"成功拨打电话: {phone_number}"
                }
            else:
                return {
                    "success": False,
                    "message": f"拨打电话失败: {phone_number}"
                }
                
        except Exception as e:
            error("立即拨号失败: %s", e)
            return {
                "success": False,
                "message": f"立即拨号失败: {str(e)}"
            }
    
    def make_immediate_call_sync(self, phone_number: str, obj_id=None) -> dict:
        """立即拨打电话（同步版本，供Flask调用）"""
        try:
            import threading
            import concurrent.futures
            
            # 使用线程池来运行异步函数，避免事件循环冲突
            def run_async_call():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(self.make_immediate_call_async(phone_number, obj_id))
                    finally:
                        loop.close()
                except Exception as e:
                    error("线程中执行立即拨号失败: %s", e)
                    return {
                        "success": False,
                        "message": f"线程中执行立即拨号失败: {str(e)}"
                    }
            
            # 在单独的线程中运行异步函数
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_async_call)
                result = future.result(timeout=300)  # 5分钟超时
                return result
                
        except Exception as e:
            error("立即拨号失败: %s", e)
            return {
                "success": False,
                "message": f"立即拨号失败: {str(e)}"
            }


# 全局调度器管理器实例
scheduler_manager = SchedulerManager()


# API接口函数（供Flask调用）
def start_scheduler():
    """启动调度器API"""
    return scheduler_manager.start_scheduler()


def stop_scheduler():
    """停止调度器API"""
    return scheduler_manager.stop_scheduler()


def restart_scheduler():
    """重启调度器API"""
    return scheduler_manager.restart_scheduler()


def get_scheduler_status():
    """获取调度器状态API"""
    return scheduler_manager.get_status()


def refresh_personnel():
    """刷新人员名单API"""
    return scheduler_manager.refresh_personnel()


def make_immediate_call(phone_number: str, obj_id:str =None):
    """立即拨打电话API"""
    return scheduler_manager.make_immediate_call_sync(phone_number, obj_id)