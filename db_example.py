"""
数据库工具模块：负责与MongoDB数据库的连接和数据操作
"""

import motor.motor_asyncio
from datetime import datetime
import json
import re
from typing import Dict, List, Optional, Any
from med_config import MONGODB_CONFIG
from db_utils import MongoDBClient


# ================================
# 数据库测试代码
# ================================

async def test_database_operations():
    """测试数据库操作，插入样例数据"""

    db = MongoDBClient()

    print("="*60)
    print("开始数据库测试 - 插入样例数据")
    print("="*60)

    try:
        # ================================
        # 1. 插入样例人员记录
        # ================================
        print("\n📋 插入样例人员记录...")

        sample_personnel = [
            {
                "name": "张三",
                "phone": "18971492577",
                "age": 35,
                "gender": "男",
                "enrollment_date": "2024-01-15",
                "training_status": "训练中"
            },
            {
                "name": "李四",
                "phone": "17764016802",
                "age": 38,
                "gender": "女",
                "enrollment_date": "2024-01-10",
                "training_status": "训练中"
            }
        ]

        sample_personnel = []

        added_personnel = []
        for person in sample_personnel:
            try:
                # 检查是否已存在
                existing = await db.get_person_by_phone(person["phone"])
                if existing:
                    print(f"  ⚠️  {person['name']} (手机号: {person['phone']}) 已存在，跳过...")
                    added_personnel.append(existing)
                    continue

                person_id = await db.add_person(person)
                added_personnel.append(person)
                print(f"  ✅ 成功添加: {person['name']} (手机号: {person['phone']}, ID: {person_id})")

            except Exception as e:
                print(f"  ❌ 添加 {person['name']} 失败: {e}")

        print(f"\n📊 人员记录插入完成，共处理 {len(sample_personnel)} 条记录")

        # ================================
        # 2. 插入样例通话记录
        # ================================
        print("\n📞 插入样例通话记录...")

        from datetime import datetime, timedelta

        # 为每个人员创建通话记录
        call_records_data = []

        # 张三的通话记录 - 训练完成，有轻微不适
        call_records_data.append({
            "患者名字": "张三",
            "手机号": "18971492577",
            "通话时间": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M"),
            "记录日期": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
            "拨号状态": "通话结束",
            "对话历史记录": [
                {"role": "assistant", "content": "您好，我是武汉协和医院骨科的智能助理，请问是张三吗？"},
                {"role": "user", "content": "是的，我是。"},
                {"role": "assistant", "content": "您好，张三。请问您昨天进行锻炼了吗？"},
                {"role": "user", "content": "是的，我昨天已经完成了锻炼。"},
                {"role": "assistant", "content": "您昨天锻炼了多少下？"},
                {"role": "user", "content": "我昨天锻炼了900下。"},
                {"role": "assistant", "content": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？"},
                {"role": "user", "content": "锻炼过程中感觉还好，但甩久了之后会感到腰部有些酸。"}
            ],
            "训练完成情况": "完成",
            "训练次数": "400次",
            "训练时长": "40分钟",
            "是否有不适感": "是",
            "不适感内容": "腰部有些酸胀",
            "是否需要医生人工和患者联系": "是",
            "锻炼辅助仪器是否有问题": "否",
            "锻炼辅助仪器问题内容": ""
        })

        # 李四的通话记录 - 训练完成，无不适
        call_records_data.append({
            "患者名字": "李四",
            "手机号": "17764016802",
            "通话时间": datetime.now().strftime("%Y-%m-%d %H:%M"),
            "记录日期": datetime.now().strftime("%Y-%m-%d"),
            "拨号状态": "通话结束",
            "对话历史记录": [
                {"role": "assistant", "content": "您好，我是武汉协和医院骨科的智能助理，请问是李四吗？"},
                {"role": "user", "content": "是的，我是。"},
                {"role": "assistant", "content": "您好，李四。请问您昨天进行锻炼了吗？"},
                {"role": "user", "content": "是的，我昨天已经完成了锻炼。"},
                {"role": "assistant", "content": "您昨天锻炼了多少下？"},
                {"role": "user", "content": "我昨天锻炼了800下。"},
                {"role": "assistant", "content": "训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？"},
                {"role": "user", "content": "感觉很好，没有什么不适。"}
            ],
            "训练完成情况": "完成",
            "训练次数": "100次",
            "训练时长": "15分钟",
            "是否有不适感": "否",
            "不适感内容": "",
            "是否需要医生人工和患者联系": "否",
            "锻炼辅助仪器是否有问题": "否",
            "锻炼辅助仪器问题内容": ""
        })


        # 插入通话记录
        added_records = 0
        for call_data in call_records_data:
            try:
                record_id = await db.add_call_record(call_data)
                added_records += 1
                print(f"  ✅ 成功添加通话记录: {call_data['患者名字']} (ID: {record_id})")

            except Exception as e:
                print(f"  ❌ 添加 {call_data['患者名字']} 的通话记录失败: {e}")

        print(f"\n📊 通话记录插入完成，成功添加 {added_records} 条记录")

        # ================================
        # 3. 验证插入结果
        # ================================
        print("\n🔍 验证插入结果...")

        # 获取统计信息
        stats = await db.get_training_statistics()
        print(f"\n📈 数据库统计信息:")
        print(f"  总人员数: {sum(stats['人员状态统计'].values())}")
        print(f"  人员状态分布: {stats['人员状态统计']}")
        print(f"  总通话次数: {stats['总通话次数']}")
        print(f"  今日通话次数: {stats['今日通话次数']}")
        print(f"  有不适感患者数: {stats['有不适感患者数']}")
        print(f"  需要医生联系患者数: {stats['需要医生联系患者数']}")

        # 查询特殊情况的患者
        print(f"\n🚨 需要关注的患者:")

        # 需要医生联系的患者
        need_contact = await db.get_patients_needing_doctor_contact()
        if need_contact:
            print(f"  📞 需要医生联系的患者 ({len(need_contact)}人):")
            for record in need_contact:
                print(f"    - {record['患者名字']} ({record['手机号']}): {record['不适感内容'] or '设备问题'}")

        # 有不适感的患者
        with_discomfort = await db.get_patients_with_discomfort()
        if with_discomfort:
            print(f"  😟 有不适感的患者 ({len(with_discomfort)}人):")
            for record in with_discomfort:
                print(f"    - {record['患者名字']} ({record['手机号']}): {record['不适感内容']}")

        # 训练未完成的记录
        incomplete = await db.get_incomplete_training_records()
        if incomplete:
            print(f"  ⚠️  训练未完成的患者 ({len(incomplete)}人):")
            for record in incomplete:
                print(f"    - {record['患者名字']} ({record['手机号']}): {record['训练完成情况']}")

        print(f"\n✅ 数据库测试完成！所有样例数据已成功插入。")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭数据库连接
        await db.close()
        print("\n🔐 数据库连接已关闭")


if __name__ == "__main__":
    """直接运行此文件进行数据库测试"""
    import asyncio

    print("🚀 启动数据库工具测试...")
    print("⚠️  注意：请确保MongoDB服务已启动")
    print("🔧 配置信息：")
    print(f"   - 连接字符串: {MONGODB_CONFIG['connection_string']}")
    print(f"   - 数据库名称: {MONGODB_CONFIG['db_name']}")

    input("\n按回车键继续...")

    asyncio.run(test_database_operations())

