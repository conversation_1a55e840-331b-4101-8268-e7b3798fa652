# AI电话机器人 - 长时间运行模式使用说明

## 概述

`long_run_main.py` 是AI电话机器人的长时间运行版本，支持在每天的多个指定时间段自动进行并发拨号，其余时间空闲等待。

## 功能特点

- 🕐 **多时间段支持**: 可配置每天的多个拨号时间段
- 🔄 **自动调度**: 自动检测当前时间，在指定时间段内执行拨号
- 📅 **每日重复**: 每天自动重复执行，无需手动干预
- 🚫 **防重复执行**: 每个时间段每天只执行一次
- ⏰ **本地时间**: 使用系统本地时间进行调度
- 📊 **状态监控**: 实时显示下一个时间段信息和执行状态
- 🔧 **灵活配置**: 通过配置文件轻松调整时间段和参数

## 配置说明

### 调度配置 (config.py)

```python
SCHEDULE_CONFIG = {
    "enabled": True,           # 是否启用调度模式
    "check_interval": 60,      # 检查间隔（秒）
    "time_slots": [            # 每天的拨号时间段
        {
            "name": "上午时段",
            "start_time": "09:00",
            "end_time": "11:30"
        },
        {
            "name": "下午时段", 
            "start_time": "14:00",
            "end_time": "17:00"
        },
        {
            "name": "晚上时段",
            "start_time": "19:00", 
            "end_time": "21:00"
        }
    ],
    "skip_weekends": False,       # 是否跳过周末
    "max_calls_per_day": None,    # 每天最大拨号次数限制（None为无限制）
}
```

### 配置参数说明

- **enabled**: 控制调度功能的开关
- **check_interval**: 系统检查时间间隔，建议60秒
- **time_slots**: 拨号时间段列表，每个时间段包含：
  - `name`: 时间段名称
  - `start_time`: 开始时间 (HH:MM格式，基于系统本地时间)
  - `end_time`: 结束时间 (HH:MM格式，基于系统本地时间)
- **skip_weekends**: 是否跳过周末（周六、周日）
- **max_calls_per_day**: 每天最大拨号次数（暂未实现）

## 使用方法

### 1. 启动长时间运行模式

```bash
python long_run_main.py
```

### 2. 程序运行流程

1. **启动阶段**: 
   - 初始化日志系统
   - 检查调度配置
   - 显示时间段配置信息

2. **运行阶段**:
   - 每隔指定间隔检查当前时间
   - 判断是否在拨号时间段内
   - 如果在时间段内且今天未执行过，则开始拨号
   - 执行完成后标记为已执行，等待下一个时间段

3. **状态显示**:
   - 显示当前执行状态
   - 显示下一个时间段的倒计时信息

### 3. 停止程序

使用 `Ctrl+C` 安全停止程序。

## 运行示例

```
=== AI电话机器人 - 长时间运行模式 ===
🚀 AI电话机器人调度器启动
⏰ 检查间隔: 60 秒
🕐 时间段配置:
   上午时段: 09:00 - 11:30
   下午时段: 14:00 - 17:00
   晚上时段: 19:00 - 21:00

💤 当前不在拨号时间段，下一个时间段: 上午时段 (09:00) - 还有 2小时15分钟

🎯 开始执行 上午时段 (09:00 - 11:30) 的拨号任务
📞 上午时段: 准备拨打 25 个电话
...
✅ 上午时段: 拨号任务完成

⏸️  当前时间段 上午时段 今天已执行，等待下一个时间段
💤 当前不在拨号时间段，下一个时间段: 下午时段 (14:00) - 还有 2小时30分钟
```

## 注意事项

1. **数据库连接**: 确保数据库服务正常运行
2. **NATS服务**: 确保NATS服务器可正常连接
3. **权限要求**: 需要拨号相关权限
4. **时间精度**: 建议检查间隔不少于60秒，避免频繁检查
5. **日志监控**: 关注日志输出，及时发现问题

## 与普通模式的区别

| 特性 | 普通模式 (main.py) | 长时间运行模式 (long_run_main.py) |
|------|-------------------|----------------------------------|
| 运行方式 | 一次性执行 | 长时间运行，定时执行 |
| 时间控制 | 立即执行 | 按时间段调度 |
| 适用场景 | 手动执行、测试 | 生产环境、自动化 |
| 资源占用 | 执行完即退出 | 持续占用资源 |

## 故障排除

1. **程序无法启动**: 检查配置文件语法和导入依赖
2. **时间段不执行**: 检查时间配置和系统时间
3. **拨号失败**: 查看日志中的详细错误信息
4. **数据库连接问题**: 检查数据库服务和连接配置

## 扩展功能

可以根据需要扩展以下功能：
- 节假日跳过逻辑
- 每日拨号次数限制
- 动态调整时间段
- 远程控制接口
- 更详细的执行报告 