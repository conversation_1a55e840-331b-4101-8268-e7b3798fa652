import websockets
import json
import asyncio
import uuid
import time
import wave
import struct
import os
from typing import Optional, AsyncGenerator


class AliyunASR:
    def __init__(self, appkey: str, token: str):
        """
        初始化阿里云语音识别客户端

        Args:
            appkey: 管控台创建的项目Appkey
            token: 通过阿里云获取的访问token
        """
        self.appkey = appkey
        self.token = token
        self.ws = None
        self.task_id = None

        # 阿里云语音服务WebSocket地址
        self.url = f"wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1?token={token}"

    def _generate_message_id(self) -> str:
        """生成32位唯一message_id"""
        return uuid.uuid4().hex

    def _generate_task_id(self) -> str:
        """生成32位唯一task_id"""
        return uuid.uuid4().hex

    async def connect(self):
        """建立WebSocket连接"""
        try:
            self.ws = await websockets.connect(self.url)
            self.task_id = self._generate_task_id()
            print(f"WebSocket连接成功，task_id: {self.task_id}")
            return True
        except Exception as e:
            print(f"WebSocket连接失败: {e}")
            return False

    async def start_transcription(self,
                                  format: str = "pcm",
                                  sample_rate: int = 16000,
                                  enable_intermediate_result: bool = True,
                                  enable_punctuation_prediction: bool = True,
                                  enable_inverse_text_normalization: bool = True,
                                  enable_words: bool = False,
                                  max_sentence_silence: int = 800):
        """
        发送StartTranscription指令开始语音识别

        Args:
            format: 音频格式 (pcm, wav, opus, speex, amr, mp3, aac)
            sample_rate: 音频采样率 (8000 或 16000)
            enable_intermediate_result: 是否返回中间识别结果
            enable_punctuation_prediction: 是否添加标点
            enable_inverse_text_normalization: 是否中文数字转阿拉伯数字
            enable_words: 是否返回词信息
            max_sentence_silence: 语音断句检测阈值(ms)
        """
        if not self.ws:
            raise Exception("WebSocket连接未建立")

        start_message = {
            "header": {
                "message_id": self._generate_message_id(),
                "task_id": self.task_id,
                "namespace": "SpeechTranscriber",
                "name": "StartTranscription",
                "appkey": self.appkey
            },
            "payload": {
                "format": format,
                "sample_rate": sample_rate,
                "enable_intermediate_result": enable_intermediate_result,
                "enable_punctuation_prediction": enable_punctuation_prediction,
                "enable_inverse_text_normalization": enable_inverse_text_normalization,
                "enable_words": enable_words,
                "max_sentence_silence": max_sentence_silence
            }
        }

        await self.ws.send(json.dumps(start_message))
        print("已发送StartTranscription指令")

        # 等待TranscriptionStarted事件
        response = await self.ws.recv()
        result = json.loads(response)

        if result.get("header", {}).get("name") == "TranscriptionStarted":
            print("语音识别已启动，可以发送音频数据")
            return True
        else:
            print(f"启动失败: {result}")
            return False

    async def stop_transcription(self):
        """发送StopTranscription指令停止语音识别"""
        if not self.ws:
            return

        stop_message = {
            "header": {
                "message_id": self._generate_message_id(),
                "task_id": self.task_id,
                "namespace": "SpeechTranscriber",
                "name": "StopTranscription",
                "appkey": self.appkey
            }
        }

        await self.ws.send(json.dumps(stop_message))
        print("已发送StopTranscription指令")

    async def send_audio_data(self, audio_data: bytes):
        """发送音频数据"""
        if not self.ws:
            raise Exception("WebSocket连接未建立")

        await self.ws.send(audio_data)

    async def receive_events(self) -> AsyncGenerator[dict, None]:
        """接收服务端事件"""
        if not self.ws:
            return

        try:
            while True:
                response = await self.ws.recv()
                event = json.loads(response)
                yield event

                # 如果收到TranscriptionCompleted事件，停止接收
                if event.get("header", {}).get("name") == "TranscriptionCompleted":
                    break

        except websockets.exceptions.ConnectionClosed:
            print("WebSocket连接已关闭")
        except Exception as e:
            print(f"接收事件时出错: {e}")

    def process_wav_file(self, file_path: str) -> tuple:
        """
        处理WAV文件，提取音频数据和参数

        Returns:
            tuple: (audio_data, sample_rate, format)
        """
        try:
            with wave.open(file_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()

                print(f"音频文件信息:")
                print(f"  文件: {file_path}")
                print(f"  采样率: {sample_rate} Hz")
                print(f"  声道数: {channels}")
                print(f"  采样位宽: {sample_width} bytes")
                print(f"  总帧数: {frames}")
                print(f"  时长: {frames / sample_rate:.2f} 秒")

                # 读取音频数据
                audio_data = wav_file.readframes(frames)

                # 转换为单声道
                if channels == 2:
                    print("转换立体声为单声道...")
                    if sample_width == 2:  # 16-bit
                        samples = struct.unpack('<{}h'.format(frames * channels), audio_data)
                        mono_samples = [samples[i] for i in range(0, len(samples), 2)]
                        audio_data = struct.pack('<{}h'.format(len(mono_samples)), *mono_samples)

                return audio_data, sample_rate, "pcm"

        except Exception as e:
            print(f"处理WAV文件时出错: {e}")
            return None, None, None

    async def recognize_file(self, file_path: str, chunk_size: int = 3200) -> list:
        """
        识别音频文件

        Args:
            file_path: 音频文件路径
            chunk_size: 每次发送的数据块大小(字节)

        Returns:
            list: 识别结果列表
        """
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return []

        # 处理音频文件
        if file_path.lower().endswith('.wav'):
            audio_data, sample_rate, format_type = self.process_wav_file(file_path)
        else:
            # 处理原始PCM数据
            with open(file_path, 'rb') as f:
                audio_data = f.read()
            sample_rate = 16000  # 默认采样率
            format_type = "pcm"

        if audio_data is None:
            print("无法读取音频数据")
            return []

        # 建立连接
        if not await self.connect():
            return []

        # 开始转录
        if not await self.start_transcription(
                format=format_type,
                sample_rate=sample_rate,
                enable_intermediate_result=True,
                enable_punctuation_prediction=True
        ):
            return []

        # 创建接收事件的任务
        results = []
        receive_task = asyncio.create_task(self._collect_results(results))

        try:
            # 分块发送音频数据
            total_chunks = (len(audio_data) + chunk_size - 1) // chunk_size
            print(f"开始发送音频数据，总大小: {len(audio_data)} bytes，分为 {total_chunks} 块")

            for i in range(0, len(audio_data), chunk_size):
                chunk = audio_data[i:i + chunk_size]
                await self.send_audio_data(chunk)

                # 显示进度
                progress = ((i // chunk_size) + 1) / total_chunks * 100
                if (i // chunk_size) % 10 == 0:
                    print(f"发送进度: {progress:.1f}%")

                # 控制发送速度
                await asyncio.sleep(0.01)

            print("音频数据发送完成")

            # 停止转录
            await self.stop_transcription()

            # 等待接收完成
            await receive_task

        except Exception as e:
            print(f"识别过程中出错: {e}")
            receive_task.cancel()

        finally:
            if self.ws:
                await self.ws.close()

        return results

    async def _collect_results(self, results: list):
        """收集识别结果"""
        async for event in self.receive_events():
            header = event.get("header", {})
            payload = event.get("payload", {})

            event_name = header.get("name")

            if event_name == "SentenceBegin":
                print(f"句子开始 - 索引: {payload.get('index')}, 时间: {payload.get('time')}ms")

            elif event_name == "TranscriptionResultChanged":
                # 中间识别结果
                result_text = payload.get("result", "")
                if result_text.strip():
                    print(f"中间结果: {result_text}")

            elif event_name == "SentenceEnd":
                # 最终识别结果
                result_text = payload.get("result", "")
                index = payload.get("index")
                confidence = payload.get("confidence", 0)

                if result_text.strip():
                    print(f"最终结果 [{index}]: {result_text} (置信度: {confidence:.3f})")
                    results.append({
                        "index": index,
                        "text": result_text,
                        "confidence": confidence,
                        "time": payload.get("time"),
                        "begin_time": payload.get("begin_time")
                    })

            elif event_name == "TranscriptionCompleted":
                print("语音识别完成")
                break

    async def real_time_recognize(self, audio_stream):
        """
        实时语音识别（适用于音频流）

        Args:
            audio_stream: 音频流生成器
        """
        # 建立连接
        if not await self.connect():
            return

        # 开始转录
        if not await self.start_transcription():
            return

        # 创建接收事件的任务
        receive_task = asyncio.create_task(self._print_real_time_results())

        try:
            # 发送音频流
            async for audio_chunk in audio_stream:
                if audio_chunk is None:  # 结束信号
                    break
                await self.send_audio_data(audio_chunk)
                await asyncio.sleep(0.01)  # 控制发送速度

            # 停止转录
            await self.stop_transcription()

            # 等待接收完成
            await receive_task

        except Exception as e:
            print(f"实时识别过程中出错: {e}")
            receive_task.cancel()

        finally:
            if self.ws:
                await self.ws.close()

    async def _print_real_time_results(self):
        """打印实时识别结果"""
        async for event in self.receive_events():
            header = event.get("header", {})
            payload = event.get("payload", {})

            event_name = header.get("name")

            if event_name == "TranscriptionResultChanged":
                result_text = payload.get("result", "")
                if result_text.strip():
                    print(f"\r实时结果: {result_text}", end="", flush=True)

            elif event_name == "SentenceEnd":
                result_text = payload.get("result", "")
                if result_text.strip():
                    print(f"\n最终结果: {result_text}")


# 使用示例
async def main():
    # 配置参数 - 需要替换为你的实际参数
    APPKEY = "your_appkey_here"  # 替换为你的AppKey
    TOKEN = "your_token_here"  # 替换为你获取的Token

    # 音频文件路径
    audio_file_path = r"D:\57_project_codes\simHehua\freeswitch_mod\asterisk_mod\wavfiles\output23.wav"

    # 创建ASR实例
    asr = AliyunASR(APPKEY, TOKEN)

    print("=== 开始文件识别 ===")

    # 识别音频文件
    results = await asr.recognize_file(audio_file_path)

    print("\n=== 识别结果汇总 ===")
    for result in results:
        print(f"[{result['index']}] {result['text']} (置信度: {result['confidence']:.3f})")

    # 如果你有完整的识别文本需求
    full_text = " ".join([result['text'] for result in results])
    print(f"\n完整识别文本: {full_text}")


# 实时音频流示例（模拟）
async def audio_stream_generator(file_path: str, chunk_size: int = 3200):
    """模拟音频流生成器"""
    try:
        with open(file_path, 'rb') as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                yield chunk
                await asyncio.sleep(0.1)  # 模拟实时音频流的时间间隔
    except Exception as e:
        print(f"读取音频流时出错: {e}")


async def real_time_example():
    """实时识别示例"""
    '''
    AccessKey ID
LTAI5t9Aw23eagCgz5WiVxMG

AccessKey Secret
******************************
    '''
    APPKEY = "yPPBQsoHYG01rMi4"
    TOKEN = "b69d4287a5fb448b852627fdfe37fa59"
    audio_file_path = r"D:\57_project_codes\simHehua\freeswitch_mod\asterisk_mod\wavfiles\output23.wav"

    asr = AliyunASR(APPKEY, TOKEN)

    print("=== 开始实时识别 ===")

    # 创建音频流
    audio_stream = audio_stream_generator(audio_file_path)

    # 开始实时识别
    await asr.real_time_recognize(audio_stream)


if __name__ == "__main__":
    # 运行文件识别示例
    asyncio.run(main())

    # 如果要运行实时识别示例，取消下面的注释
    # asyncio.run(real_time_example())