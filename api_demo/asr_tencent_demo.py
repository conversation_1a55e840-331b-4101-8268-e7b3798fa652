import websockets
import json
import asyncio
import hmac
import base64
import time
import urllib.parse
from urllib.parse import quote
import uuid
import wave
import struct


# 如果没有这些模块，可以注释掉或创建简单的替代
# from llm_server.queue_man import QueueManager
# from llm_server.wav_saver import writeframe


class SimpleQueue:
    """简单的队列实现，替代QueueManager"""

    def __init__(self):
        self.queue = asyncio.Queue()

    async def put(self, item):
        await self.queue.put(item)

    async def get(self):
        return await self.queue.get()


class TencentASR:
    def __init__(self, app_id, secret_id, secret_key):
        self.app_id = app_id
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.ws = None

    def _generate_signature(self, params):
        sorted_params = sorted(params.items(), key=lambda x: x[0])
        raw_str = f"asr.cloud.tencent.com/asr/v2/{self.app_id}?"
        raw_str += "&".join([f"{k}={v}" for k, v in sorted_params])
        hmacstr = hmac.new(self.secret_key.encode('utf-8'),
                           raw_str.encode('utf-8'),
                           digestmod='SHA1').digest()
        signature = base64.b64encode(hmacstr).decode('utf-8')
        return signature

    def _generate_url(self, sample_rate):
        current_time = int(time.time())
        params = {
            'secretid': self.secret_id,
            'timestamp': str(current_time),
            'expired': str(current_time + 24 * 60 * 60),
            'nonce': str(current_time),
            'engine_model_type': f'16k_zh_dialect',
            'voice_id': str(uuid.uuid4()),
            'voice_format': '1',  # PCM格式
            'needvad': 1,
            'vad_silence_time': 800,
            'word_info': '2',
            'filter_empty_result': 1
        }

        signature = self._generate_signature(params)
        params['signature'] = signature
        url = f"wss://asr.cloud.tencent.com/asr/v2/{self.app_id}?"
        url += "&".join([f"{k}={quote(str(v))}" for k, v in params.items()])
        return url

    async def connect(self, sample_rate):
        url = self._generate_url(sample_rate)
        self.ws = await websockets.connect(url)
        response = await self.ws.recv()
        return json.loads(response)

    def process_wav_file(self, file_path):
        """处理WAV文件，提取PCM数据和参数"""
        try:
            with wave.open(file_path, 'rb') as wav_file:
                # 获取音频参数
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()

                print(f"音频文件信息:")
                print(f"  采样率: {sample_rate} Hz")
                print(f"  声道数: {channels}")
                print(f"  采样位宽: {sample_width} bytes")
                print(f"  总帧数: {frames}")
                print(f"  时长: {frames / sample_rate:.2f} 秒")

                # 读取所有音频数据
                audio_data = wav_file.readframes(frames)

                # 如果是立体声，转换为单声道
                if channels == 2:
                    print("转换立体声为单声道...")
                    if sample_width == 2:  # 16-bit
                        samples = struct.unpack('<{}h'.format(frames * channels), audio_data)
                        mono_samples = [samples[i] for i in range(0, len(samples), 2)]
                        audio_data = struct.pack('<{}h'.format(len(mono_samples)), *mono_samples)
                    elif sample_width == 1:  # 8-bit
                        samples = struct.unpack('<{}B'.format(frames * channels), audio_data)
                        mono_samples = [samples[i] for i in range(0, len(samples), 2)]
                        audio_data = struct.pack('<{}B'.format(len(mono_samples)), *mono_samples)

                # 如果采样率不是8kHz或16kHz，需要重采样（这里简化处理）
                if sample_rate not in [8000, 16000]:
                    print(f"警告: 采样率 {sample_rate} 可能不被支持，建议使用 8kHz 或 16kHz")

                return audio_data, sample_rate

        except Exception as e:
            print(f"处理WAV文件时出错: {e}")
            return None, None

    def process_raw_audio(self, file_path, sample_rate=8000, sample_width=2, channels=1):
        """处理原始音频文件（非WAV格式）"""
        try:
            with open(file_path, "rb") as f:
                audio_data = f.read()

            print(f"原始音频文件信息:")
            print(f"  文件大小: {len(audio_data)} bytes")
            print(f"  假设采样率: {sample_rate} Hz")
            print(f"  假设采样位宽: {sample_width} bytes")
            print(f"  假设声道数: {channels}")

            # 计算时长
            bytes_per_second = sample_rate * sample_width * channels
            duration = len(audio_data) / bytes_per_second
            print(f"  估计时长: {duration:.2f} 秒")

            return audio_data, sample_rate

        except Exception as e:
            print(f"处理原始音频文件时出错: {e}")
            return None, None

    async def send_audio_file(self, audio_data, sample_rate):
        """发送音频文件数据"""
        if not self.ws:
            raise Exception("WebSocket connection not established")

        # 计算数据包大小 (200ms 音频)
        ms = 200
        chunk_size = (sample_rate * 2 * ms) // 1000

        print(f"开始发送音频数据，数据包大小: {chunk_size} bytes")

        # 分块发送音频数据
        total_chunks = (len(audio_data) + chunk_size - 1) // chunk_size

        for i in range(0, len(audio_data), chunk_size):
            chunk = audio_data[i:i + chunk_size]

            # 如果最后一个数据包不完整，用零填充
            if len(chunk) < chunk_size:
                chunk = chunk + b'\x00' * (chunk_size - len(chunk))

            # 发送音频数据
            await self.ws.send(chunk)

            # 接收响应
            try:
                response = await asyncio.wait_for(self.ws.recv(), timeout=0.1)
                result = json.loads(response)

                if result.get('code') != 0:
                    print(f"错误响应: {result}")
                    continue

                if 'result' in result and 'voice_text_str' in result['result']:
                    yield result

            except asyncio.TimeoutError:
                continue
            except websockets.exceptions.ConnectionClosed:
                print("连接已关闭")
                break
            except Exception as e:
                print(f"接收响应时出错: {str(e)}")
                continue

            # 进度显示
            progress = ((i // chunk_size) + 1) / total_chunks * 100
            if (i // chunk_size) % 10 == 0:  # 每10个数据包显示一次进度
                print(f"发送进度: {progress:.1f}%")

            # 控制发送速度，避免过快
            await asyncio.sleep(0.01)

    async def finish(self):
        if self.ws:
            print("发送结束信号")
            await self.ws.send(json.dumps({"type": "end"}))
            try:
                while True:
                    try:
                        response = await asyncio.wait_for(self.ws.recv(), timeout=2.0)
                        result = json.loads(response)
                        print(f"收到最终响应: {result}")
                        if result.get('final') == 1:
                            break
                        yield result
                    except asyncio.TimeoutError:
                        print("等待最终响应超时")
                        break
            except websockets.exceptions.ConnectionClosed:
                print("连接已关闭")
            finally:
                await self.ws.close()


async def asr_file_processor(file_path, is_wav=True):
    """处理本地音频文件的ASR识别"""
    print('Starting ASR processor for local file...')

    # 创建ASR实例
    asr = TencentASR(
        app_id="1258989328",
        secret_id="AKIDKLFQbeVEoGansnY8Yv8A2jHeppeKo7FE",
        secret_key="zvigfZQDsPsAJQVZSCuX4RehZFk7LMAn"
    )

    # 处理音频文件
    if is_wav:
        audio_data, sample_rate = asr.process_wav_file(file_path)
    else:
        # 对于原始音频文件，需要指定参数
        audio_data, sample_rate = asr.process_raw_audio(file_path, sample_rate=8000)

    if audio_data is None:
        print("无法读取音频文件")
        return

    # 建立连接
    result = await asr.connect(sample_rate)
    print(f"ASR连接结果: {result}")

    print(f"\n开始识别音频文件: {file_path}")
    print(f"音频数据大小: {len(audio_data)} bytes")

    # 发送音频数据并获取识别结果
    async for result in asr.send_audio_file(audio_data, sample_rate):
        if 'result' in result and 'voice_text_str' in result['result']:
            slice_type = result['result'].get('slice_type', 0)
            text = result['result']['voice_text_str']

            if text.strip():  # 只打印非空结果
                if slice_type == 2:  # 最终结果
                    print(f"最终识别结果: {text}")
                else:  # 临时结果
                    print(f"临时识别结果: {text}")

    print("\n获取最终结果...")
    # 发送结束信号并获取最终结果
    async for result in asr.finish():
        if 'result' in result and 'voice_text_str' in result['result']:
            text = result['result']['voice_text_str']
            if text.strip():
                print(f"最终识别结果: {text}")


if __name__ == "__main__":
    # 音频文件路径
    audio_file_path = r"D:\57_project_codes\simHehua\freeswitch_mod\asterisk_mod\wavfiles\output23.wav"

    # 如果是WAV文件，设置is_wav=True；如果是原始PCM数据，设置is_wav=False
    is_wav_file = audio_file_path.lower().endswith('.wav')

    # 运行识别
    asyncio.run(asr_file_processor(audio_file_path, is_wav=is_wav_file))