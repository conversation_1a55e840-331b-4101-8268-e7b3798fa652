# MongoDB 数据库架构文档

## 概述

本项目使用MongoDB作为数据存储解决方案，主要用于管理医疗随访系统中的患者信息和通话记录。数据库包含两个主要集合（Collections）：人员名单和通话记录。

## 数据库配置

在 `config.py` 中配置MongoDB连接信息：

```python
MONGODB_CONFIG = {
    "connection_string": "mongodb://localhost:27017",
    "db_name": "med_call_records"
}
```

## 集合架构

### 1. 人员名单集合 (personnel)

存储需要电话随访的患者基本信息。

#### 字段说明

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| `_id` | ObjectId | 是 | MongoDB自动生成的唯一标识 | `ObjectId("...")` |
| `name` | String | 是 | 患者姓名 | `"张三"` |
| `phone` | String | 是 | 手机号码，唯一标识 | `"18912345678"` |
| `age` | Number | 是 | 年龄 | `45` |
| `gender` | String | 是 | 性别 | `"男"` 或 `"女"` |
| `enrollment_date` | String | 是 | 入组时间（YYYY-MM-DD格式） | `"2024-01-15"` |
| `training_status` | String | 是 | 当前训练状态 | `"训练中"` |
| `created_at` | DateTime | 是 | 记录创建时间 | `ISODate("...")` |
| `updated_at` | DateTime | 是 | 记录更新时间 | `ISODate("...")` |

#### 训练状态值

- `"训练中"` - 正在进行康复训练
- `"未开始"` - 尚未开始训练
- `"暂停"` - 暂停训练
- `"终止"` - 终止训练
- `"休息"` - 休息中

#### 示例文档

```json
{
  "_id": ObjectId("65a1b2c3d4e5f6789abcdef0"),
  "name": "张三",
  "phone": "18912345678",
  "age": 45,
  "gender": "男",
  "enrollment_date": "2024-01-15",
  "training_status": "训练中",
  "created_at": ISODate("2024-01-20T10:30:00Z"),
  "updated_at": ISODate("2024-01-20T10:30:00Z")
}
```

### 2. 通话记录集合 (call_records)

存储每次通话的详细记录和患者训练情况。

#### 字段说明

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| `_id` | ObjectId | 是 | MongoDB自动生成的唯一标识 | `ObjectId("...")` |
| `患者名字` | String | 是 | 患者姓名 | `"张三"` |
| `手机号` | String | 是 | 患者手机号 | `"18912345678"` |
| `通话时间` | String | 是 | 通话开始时间（YYYY-MM-DD HH:MM格式） | `"2024-01-20 14:30"` |
| `记录日期` | String | 是 | 记录日期（YYYY-MM-DD格式） | `"2024-01-20"` |
| `对话历史记录` | Array | 否 | 完整对话记录 | `[{role: "assistant", content: "..."}, ...]` |
| `训练完成情况` | String | 否 | 训练是否完成及原因 | `"完成"` 或 `"未完成，身体不适"` |
| `训练次数` | String | 否 | 训练次数 | `"900次"` |
| `训练时长` | String | 否 | 训练持续时间 | `"50分钟"` |
| `是否有不适感` | String | 否 | 是否有不适症状 | `"是"` 或 `"否"` |
| `不适感内容` | String | 否 | 具体不适症状描述 | `"腰部酸胀"` |
| `是否需要医生人工和患者联系` | String | 否 | 是否需要医生介入 | `"是"` 或 `"否"` |
| `锻炼辅助仪器是否有问题` | String | 否 | 设备是否正常 | `"是"` 或 `"否"` |
| `锻炼辅助仪器问题内容` | String | 否 | 设备问题描述 | `"绑带松动"` |
| `创建时间` | DateTime | 是 | 记录创建时间 | `ISODate("...")` |

#### 示例文档

```json
{
  "_id": ObjectId("65a1b2c3d4e5f6789abcdef1"),
  "患者名字": "张三",
  "手机号": "18912345678",
  "通话时间": "2024-01-20 14:30",
  "记录日期": "2024-01-20",
  "对话历史记录": [
    {"role": "assistant", "content": "您好，我是武汉协和医院骨科的智能助理，请问是张三吗？"},
    {"role": "user", "content": "是的，我是。"},
    {"role": "assistant", "content": "您好，张三。请问您昨天进行锻炼了吗？"},
    {"role": "user", "content": "是的，我昨天已经完成了锻炼。"}
  ],
  "训练完成情况": "完成",
  "训练次数": "900次",
  "训练时长": "50分钟",
  "是否有不适感": "是",
  "不适感内容": "腰部有些酸胀",
  "是否需要医生人工和患者联系": "是",
  "锻炼辅助仪器是否有问题": "否",
  "锻炼辅助仪器问题内容": "",
  "创建时间": ISODate("2024-01-20T14:30:00Z")
}
```

## 数据库操作API

### MongoDBClient 类

位于 `db_utils.py` 文件中，提供完整的CRUD操作接口。

#### 初始化

```python
from db_utils import MongoDBClient

# 使用默认配置
db = MongoDBClient()

# 使用自定义配置
db = MongoDBClient(
    connection_string="mongodb://localhost:27017",
    db_name="custom_db"
)
```

#### 人员名单操作

```python
# 添加人员
person_data = {
    "name": "张三",
    "phone": "18912345678",
    "age": 45,
    "gender": "男",
    "enrollment_date": "2024-01-15",
    "training_status": "训练中"
}
person_id = await db.add_person(person_data)

# 查询人员
person = await db.get_person_by_phone("18912345678")
person = await db.get_person_by_name("张三")

# 更新训练状态
success = await db.update_person_training_status("18912345678", "暂停")

# 获取所有人员
all_personnel = await db.get_all_personnel()
# 按状态筛选
training_personnel = await db.get_all_personnel(training_status="训练中")

# 删除人员
success = await db.delete_person("18912345678")
```

#### 通话记录操作

```python
# 添加通话记录
call_data = {
    "患者名字": "张三",
    "手机号": "18912345678",
    "通话时间": "2024-01-20 14:30",
    "记录日期": "2024-01-20",
    "训练完成情况": "完成",
    "训练次数": "900次",
    # ... 其他字段
}
record_id = await db.add_call_record(call_data)

# 查询通话记录
records = await db.get_call_records_by_phone("18912345678", limit=10)
records = await db.get_call_records_by_name("张三", limit=10)
records = await db.get_call_records_by_date_range("2024-01-01", "2024-01-31")

# 更新对话历史
chat_history = [...]
success = await db.update_call_record_chat_history(record_id, chat_history)
```

#### 特殊查询

```python
# 需要医生联系的患者
need_contact = await db.get_patients_needing_doctor_contact()

# 有不适感的患者
with_discomfort = await db.get_patients_with_discomfort()

# 训练未完成的记录
incomplete = await db.get_incomplete_training_records()

# 获取统计信息
stats = await db.get_training_statistics()
```

## 索引建议

为提高查询性能，建议创建以下索引：

```javascript
// 人员名单集合索引
db.personnel.createIndex({"phone": 1}, {unique: true})
db.personnel.createIndex({"name": 1})
db.personnel.createIndex({"training_status": 1})

// 通话记录集合索引
db.call_records.createIndex({"手机号": 1})
db.call_records.createIndex({"患者名字": 1})
db.call_records.createIndex({"记录日期": 1})
db.call_records.createIndex({"通话时间": -1})
db.call_records.createIndex({"是否需要医生人工和患者联系": 1})
db.call_records.createIndex({"是否有不适感": 1})
```

## 使用示例

参考 `db_example.py` 文件中的完整使用示例。

```bash
# 运行示例
python db_example.py
```

## 注意事项

1. **连接管理**: 使用完数据库后记得调用 `await db.close()` 关闭连接
2. **异常处理**: 所有数据库操作都应该包含在try-catch块中
3. **字段验证**: 添加数据时会自动验证必填字段
4. **状态值**: 训练状态只接受预定义的值
5. **时间格式**: 严格按照指定格式存储时间数据

## 扩展建议

1. **数据备份**: 定期备份MongoDB数据
2. **性能监控**: 监控查询性能，根据需要添加索引
3. **数据归档**: 考虑对历史数据进行归档
4. **分片策略**: 数据量大时考虑分片
5. **安全认证**: 生产环境启用MongoDB认证 