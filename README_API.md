# AI电话机器人 - API管理功能

## 概述

本系统提供了一套完整的API接口，用于管理AI电话机器人的长时间运行调度器。通过这些API，您可以：

1. **启动/停止/重启调度器** - 控制长时间运行系统
2. **立即拨打电话** - 对特定患者立即拨号
3. **刷新人员名单** - 重新加载数据库中的患者信息
4. **获取系统状态** - 查看调度器运行状态

## 文件结构

```
├── call_service.py         # 独立的拨号服务模块
├── scheduler_manager.py    # 调度器管理器（主要API接口）
├── long_run_main.py       # 长时间运行主程序（已修改）
├── api_example.py         # API使用示例
└── README_API.md          # 本文档
```

## 核心功能

### 1. 调度器管理

使用多进程方式管理长时间运行系统，避免事件循环冲突：

```python
from scheduler_manager import start_scheduler, stop_scheduler, restart_scheduler

# 启动调度器
result = start_scheduler()
# 返回：{"success": True/False, "message": "...", "pid": 进程ID}

# 停止调度器  
result = stop_scheduler()
# 返回：{"success": True/False, "message": "..."}

# 重启调度器
result = restart_scheduler()
# 返回：{"success": True/False, "message": "..."}
```

### 2. 立即拨号

对特定患者立即拨打电话：

```python
from scheduler_manager import make_immediate_call

# 立即拨打电话
result = make_immediate_call("13800138000")
# 返回：{"success": True/False, "message": "..."}
```

### 3. 刷新人员名单

重新从数据库加载患者信息，只对"训练中"状态的患者拨号：

```python
from scheduler_manager import refresh_personnel

# 刷新人员名单
result = refresh_personnel()
# 返回：{"success": True/False, "message": "..."}
```

### 4. 获取系统状态

查看调度器当前运行状态：

```python
from scheduler_manager import get_scheduler_status

# 获取状态
status = get_scheduler_status()
# 返回：{"success": True/False, "status": "running/stopped", "message": "...", "pid": 进程ID}
```

## Flask集成示例

```python
from flask import Flask, jsonify, request
from scheduler_manager import (
    start_scheduler,
    stop_scheduler, 
    restart_scheduler,
    get_scheduler_status,
    refresh_personnel,
    make_immediate_call
)

app = Flask(__name__)

@app.route('/api/scheduler/start', methods=['POST'])
def api_start_scheduler():
    """启动调度器"""
    result = start_scheduler()
    return jsonify(result)

@app.route('/api/scheduler/stop', methods=['POST']) 
def api_stop_scheduler():
    """停止调度器"""
    result = stop_scheduler()
    return jsonify(result)

@app.route('/api/scheduler/restart', methods=['POST'])
def api_restart_scheduler():
    """重启调度器"""
    result = restart_scheduler()
    return jsonify(result)

@app.route('/api/scheduler/status', methods=['GET'])
def api_get_scheduler_status():
    """获取调度器状态"""
    result = get_scheduler_status()
    return jsonify(result)

@app.route('/api/scheduler/refresh', methods=['POST'])
def api_refresh_personnel():
    """刷新人员名单"""
    result = refresh_personnel()
    return jsonify(result)

@app.route('/api/call/immediate', methods=['POST'])
def api_make_immediate_call():
    """立即拨打电话"""
    data = request.get_json()
    phone_number = data.get('phone')
    if not phone_number:
        return jsonify({"success": False, "message": "缺少手机号参数"})
    
    result = make_immediate_call(phone_number)
    return jsonify(result)

if __name__ == '__main__':
    app.run(debug=True)
```

## API接口说明

### 1. 启动调度器
- **路径**: `/api/scheduler/start`
- **方法**: POST
- **返回**: 
  ```json
  {
    "success": true,
    "message": "调度器启动成功，进程ID: 12345",
    "pid": 12345
  }
  ```

### 2. 停止调度器
- **路径**: `/api/scheduler/stop`
- **方法**: POST
- **返回**:
  ```json
  {
    "success": true,
    "message": "调度器已停止"
  }
  ```

### 3. 重启调度器
- **路径**: `/api/scheduler/restart`
- **方法**: POST
- **返回**:
  ```json
  {
    "success": true,
    "message": "调度器重启成功，新进程ID: 12346"
  }
  ```

### 4. 获取调度器状态
- **路径**: `/api/scheduler/status`
- **方法**: GET
- **返回**:
  ```json
  {
    "success": true,
    "status": "running",
    "message": "调度器正在运行，进程ID: 12345",
    "pid": 12345
  }
  ```

### 5. 刷新人员名单
- **路径**: `/api/scheduler/refresh`
- **方法**: POST
- **返回**:
  ```json
  {
    "success": true,
    "message": "已发送刷新命令到调度器，将在下次拨号时生效"
  }
  ```

### 6. 立即拨打电话
- **路径**: `/api/call/immediate`
- **方法**: POST
- **请求体**:
  ```json
  {
    "phone": "13800138000"
  }
  ```
- **返回**:
  ```json
  {
    "success": true,
    "message": "成功拨打电话: 13800138000"
  }
  ```

## 重要特性

### 1. 多进程管理
- 使用`multiprocessing`模块管理调度器进程
- 避免与现有电话系统的事件循环冲突
- 支持优雅停止和强制终止

### 2. 训练状态过滤
- 自动只对"训练中"状态的患者拨号
- 支持刷新人员名单，重新加载数据库数据
- 可配置的训练状态过滤条件

### 3. 错误处理
- 完善的异常处理机制
- 详细的错误信息返回
- 日志记录支持

### 4. 独立模块设计
- 从`main.py`中分离出独立的拨号服务
- 避免循环依赖
- 模块化设计，便于维护

## 使用说明

1. **配置检查**: 确保`config.py`中的`SCHEDULE_CONFIG`已正确配置
2. **数据库连接**: 确保MongoDB连接正常
3. **导入模块**: 在您的Flask应用中导入相应的函数
4. **API集成**: 根据示例代码集成API接口
5. **测试**: 使用`api_example.py`测试功能

## 注意事项

1. **进程管理**: 调度器运行在独立进程中，确保系统有足够的资源
2. **数据库状态**: 刷新人员名单时会重新查询数据库
3. **并发控制**: 立即拨号和调度器拨号共享相同的并发控制配置
4. **日志记录**: 所有操作都有详细的日志记录
5. **错误恢复**: 系统支持自动错误恢复和重启机制

## 运行示例

```bash
# 运行API示例
python api_example.py

# 直接运行长时间调度器（用于测试）
python long_run_main.py
``` 