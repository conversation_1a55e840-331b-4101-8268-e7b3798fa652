import requests
import json

# webhook地址
webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f4ba3c6c-d8f7-422e-a880-eeebf4bdf3ad"

# 患者数据
patient_data = {
  "姓名": "王",
  "患者电话": "1027",
  "是否接通": True,
  "入组时间": "2025-02-24 12:36:41",
  "今天是否完成训练": "是",
  "休息时间": "没有提及",
  "实验阶段": 1,
  "受试者编号": "3号",
  "组别": "21 - 5的组",
  "左腿甩腿次数": "140次",
  "右腿甩腿次数": "120次",
  "不适感": "没有",
  "不适感缓解情况": "没有提及",
  "明天是否继续使用产品": "是",
  "额外活动": "没有",
  "挂断类型": "AI 挂断",
  "挂断原因": "用户主动要求",
  "dial_string": "user/1027",
  "start_timestamp": "2025-02-24_12-36-41",
  "end_timestamp": "02-24_12-38-54",
  "通话时长": "0:02:12.737922"
}

# 构建markdown内容
markdown_content = f"""# 患者训练情况报告 📋

## 📌 基本信息
**姓名**：<font color="info">{patient_data['姓名']}</font>
**受试者编号**：<font color="info">{patient_data['受试者编号']}</font>
**组别**：<font color="info">{patient_data['组别']}</font>
**实验阶段**：<font color="info">{patient_data['实验阶段']}</font>
**入组时间**：<font color="comment">{patient_data['入组时间']}</font>

## 🏃 训练数据
> **今天是否完成训练**：<font color="green">{patient_data['今天是否完成训练']}</font>
> **左腿甩腿次数**：<font color="warning">{patient_data['左腿甩腿次数']}</font> ⭐
> **右腿甩腿次数**：<font color="warning">{patient_data['右腿甩腿次数']}</font> ⭐
> **不适感**：<font color="green">{patient_data['不适感']}</font>
> **休息时间**：{patient_data['休息时间']}

## 📞 通话信息
**患者电话**：{patient_data['患者电话']}
**是否接通**：{'是' if patient_data['是否接通'] else '否'}
**通话时长**：<font color="comment">{patient_data['通话时长'][:7]}</font>
**挂断类型**：{patient_data['挂断类型']}
**挂断原因**：{patient_data['挂断原因']}

## 🔄 后续安排
**明天是否继续使用产品**：<font color="green">{patient_data['明天是否继续使用产品']}</font>
**额外活动**：{patient_data['额外活动']}"""

# 构建请求数据
data = {
    "msgtype": "markdown",
    "markdown": {
        "content": markdown_content
    }
}

# 发送请求
headers = {'Content-Type': 'application/json'}
response = requests.post(webhook_url, headers=headers, data=json.dumps(data))

# 打印响应结果
print(response.status_code)
print(response.json())